package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import com.google.gwt.event.shared.EventHandler;

/**
 * Обработчик события изменения режима маски ввода на форме добавления/редактирования атрибута
 *
 * <AUTHOR>
 * @since 05.11.2020
 */
public interface InputMaskModeChangeHandler extends EventHandler
{
    void onInputMaskModeChange(InputMaskModeChangeEvent event);
}
