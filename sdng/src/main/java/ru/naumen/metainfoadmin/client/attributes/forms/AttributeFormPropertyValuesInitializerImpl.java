package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.dispatch2.GetHideArchivedSettingsAction;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Никаких исходных значений - для формы добавления
 * <AUTHOR>
 * @since 01.06.2012
 */
public class AttributeFormPropertyValuesInitializerImpl<F extends ObjectForm> implements
        AttributeFormPropertyValuesInitializer<F>
{
    @Inject
    protected DispatchAsync dispatch;

    @Override
    public void init(IProperties contextProps, IProperties propertyValues, Attribute attribute,
            ReadyState rs)
    {
    }

    @Override
    public void loadRelatedData(IProperties contextProps, IProperties propertyValues, BasicCallback<Void> basicCallback)
    {
        propertyValues.setProperty(SCRIPT, null);
        propertyValues.setProperty(SCRIPT_FOR_FILTRATION, null);
        propertyValues.setProperty(COMPUTABLE_ON_FORM_SCRIPT, null);
        propertyValues.setProperty(SCRIPT_FOR_DEFAULT, null);
        dispatch.execute(new GetHideArchivedSettingsAction(), new BasicCallback<SimpleResult<Boolean>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<Boolean> result)
            {
                contextProps.setProperty(ENABLE_HIDE_ARCHIVED_OBJECTS, result.get());
                basicCallback.onSuccess(null);
            }
        });
    }
}
