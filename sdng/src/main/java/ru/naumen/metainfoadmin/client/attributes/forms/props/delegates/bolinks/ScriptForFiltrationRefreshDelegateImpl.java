package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks;

import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 15.08.2012
 *
 */
public class ScriptForFiltrationRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ScriptComponentEditProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean filteredByScript = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.FILTERED_BY_SCRIPT);
        if (!filteredByScript)
        {
            context.setProperty(AttributeFormPropertyCode.SCRIPT_FOR_FILTRATION, null);
        }
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        callback.onSuccess(filteredByScript && !computable && !determinable);
    }
}
