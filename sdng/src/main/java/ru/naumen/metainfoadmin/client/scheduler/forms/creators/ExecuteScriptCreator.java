package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentEditWidget;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDto;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDtoFactory;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * <AUTHOR>
 * @since 31.05.2011
 *
 */
public class ExecuteScriptCreator extends FormPropertiesCreator implements SchedulerTaskCreator
{
    @Inject
    Processor validation;
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_EDIT)
    Property<ScriptDto> script;
    @Inject
    SchedulerTaskMessages messages;
    @Inject
    private CommonMessages cmessages;
    ExecuteScriptTaskDto schTask;

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        script.setCaption(cmessages.script());
        script.setValidationMarker(true);

        ScriptComponentEditWidget scriptWidget = (ScriptComponentEditWidget)script.getValueWidget();
        scriptWidget.enableValidation();
        scriptWidget.setParentDisplay(display);
    }

    @Override
    public void bindProperties()
    {
        add(script);

        ScriptComponentEditWidget scriptWidget = (ScriptComponentEditWidget)script.getValueWidget();
        scriptWidget.init(true, OtherCategories.SCHEDULER_TASK, null);
        scriptWidget.initValidation(validation);
        DebugIdBuilder.ensureDebugId(script, "edit-script");

        if (null != schTask)
        {
            ScriptDto scriptDto = ScriptDtoFactory.createNeedLoad(schTask.getScript());
            if (schTask.getScriptDto() != null)
            {
                scriptDto = schTask.getScriptDto();
            }
            script.setValue(scriptDto);
        }
        else
        {
            script.setValue(ScriptDtoFactory.createNew());
        }
    }

    @Override
    public SchedulerTask getSchedulerTask()
    {
        if (!validation.validate())
        {
            return null;
        }
        ExecuteScriptTask task = schTask;
        if (null == task)
        {
            task = new ExecuteScriptTask();
        }
        return ExecuteScriptTaskDtoFactory.create(task, script.getValue());
    }

    @Override
    public void init(SchedulerTask schTask)
    {
        if (schTask instanceof ExecuteScriptTaskDto)
        {
            this.schTask = (ExecuteScriptTaskDto)schTask;
            return;
        }
        if (schTask != null)
        {
            this.schTask = ExecuteScriptTaskDtoFactory.create((ExecuteScriptTask)schTask, null);
        }
    }
}
