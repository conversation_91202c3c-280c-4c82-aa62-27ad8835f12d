package ru.naumen.metainfoadmin.client.attributes;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.components.block.TitledBlockWithToolDisplay;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.shared.IHasTitle;

/**
 * Дисплей информации о Классе/Типе
 *
 * <AUTHOR>
 * @since 14.10.2010
 *
 */
public interface InfoDisplay extends TitledBlockWithToolDisplay, HasProperties, IHasTitle
{
    /**
     * Добавление свойства в список после указанного.
     * @param property добавляемое свойство
     * @param after информация об элементе списка, после которого осуществляется добавление
     * @return информацию о добавленном элементе
     */
    <T, R> PropertyRegistration<T> addPropertyAfter(Property<T> property, PropertyRegistration<R> after);

    void hideTitle();

    void showTitle();

    default void setEventBus(EventBus eventBus)
    {

    }
}
