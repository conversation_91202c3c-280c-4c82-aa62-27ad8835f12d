package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dispatch.GetNamingHelpAction;
import ru.naumen.core.shared.dispatch.GetNamingHelpResponse;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

public class GenRuleBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<String, FootedTextBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, String, FootedTextBoxProperty>
{
    @Inject
    protected AdminDialogMessages messages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    protected DispatchAsync service;
    @Inject
    protected Dialogs dialog;
    @Inject
    protected Processor validation;
    @Inject
    private NotEmptyValidator notEmptyValidator;

    @Override
    public void bindProperty(final PropertyContainerContext context, FootedTextBoxProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().setFootedText(messages.help());
        property.setCaption(getCaption(context));
        ensureDebugId(property);
        property.setValidationMarker(true);
        setValidationOn(property, context);

        property.getValueWidget().addClickHandler(new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                String attrTypeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
                MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
                onNamingHelp(metaClass.getFqn(), attrTypeCode);
            }
        });

        super.bindProperty(context, property, callback);
    }

    protected void ensureDebugId(FootedTextBoxProperty property)
    {
        property.ensureDebugId("genRule");
    }

    protected String getCaption(PropertyContainerContext context)
    {
        if (IntegerAttributeType.CODE.equals(context.getPropertyValues().<String> getProperty(
                AttributeFormPropertyCode.ATTR_TYPE)))
        {
            return cmessages.numberRule();
        }
        return cmessages.nameRule();
    }

    protected void onNamingHelp(ClassFqn fqn, String attrTypeCode)
    {
        GetNamingHelpAction a = new GetNamingHelpAction(fqn, attrTypeCode);
        service.execute(a, new BasicCallback<GetNamingHelpResponse>()
        {

            @Override
            protected void handleSuccess(GetNamingHelpResponse response)
            {
                StringBuilder sb = new StringBuilder();
                sb.append("<ol class=\"").append(WidgetResources.INSTANCE.all().onNamingHelp()).append("\">");
                for (String unit : response.getUnits())
                {
                    sb.append("<li>").append(SafeHtmlUtils.htmlEscape(unit)).append("</li>");
                }
                sb.append("</ol>");

                dialog.info(messages.help(), sb.toString());
            }
        });
    }

    protected void setValidationOn(FootedTextBoxProperty property, PropertyContainerContext context)
    {
        validation.validate(property, notEmptyValidator);
    }
}
