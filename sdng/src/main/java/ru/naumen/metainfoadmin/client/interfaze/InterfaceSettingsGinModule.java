package ru.naumen.metainfoadmin.client.interfaze;

import java.util.ArrayList;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.menu.NavigationCommonGinModule;
import ru.naumen.core.client.personalsettings.locales.Locale;
import ru.naumen.core.client.personalsettings.locales.LocaleSettingsGinModule;
import ru.naumen.core.client.personalsettings.locales.LocaleSettingsGinModule.LocaleProvider;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceTabSettingsGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule;

/**
 * <AUTHOR>
 * @since Mar 29, 2013
 */
public class InterfaceSettingsGinModule extends AbstractGinModule
{
    public static class EventLocaleProvider implements Provider<ArrayList<Locale>>
    {
        @Override
        public ArrayList<Locale> get()
        {
            ArrayList<Locale> result = LocaleProvider.getAvailableLocales();
            result.sort(LocaleSettingsGinModule.LOCALE_COMPARATOR);
            return result;
        }
    }

    public static final String EVENT_LOCALES = "eventLocales";

    @Override
    protected void configure()
    {
        install(new NavigationTabSettingsGinModule());
        install(new InterfaceTabSettingsGinModule());
        install(new EditNavigationSettingsFormGinModule());
        install(new NavigationCommonGinModule());

        //@formatter:off       
        
        bind(new TypeLiteral<ArrayList<Locale>>(){})
            .annotatedWith(Names.named(EVENT_LOCALES))
            .toProvider(EventLocaleProvider.class)
            .in(Singleton.class);
        //@formatter:on
    }

}
