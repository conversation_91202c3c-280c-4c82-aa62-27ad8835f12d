package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sets;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Делегат обновления поля "Комплект" на формах добавления/редактирования атрибута.
 *
 * <AUTHOR>
 * @since 06.02.2024
 */
@Singleton
public class SettingsSetAttributeFormRefreshDelegate<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxWithEmptyOptProperty>
{
    private final SettingsSetOnFormCreator settingsSetOnFormCreator;

    @Inject
    public SettingsSetAttributeFormRefreshDelegate(SettingsSetOnFormCreator settingsSetOnFormCreator)
    {
        this.settingsSetOnFormCreator = settingsSetOnFormCreator;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(settingsSetOnFormCreator.isDisplayedOnForms());
    }
}
