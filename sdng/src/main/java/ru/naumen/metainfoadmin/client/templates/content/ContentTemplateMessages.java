package ru.naumen.metainfoadmin.client.templates.content;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Локализация раздела «Шаблоны контентов».
 * <AUTHOR>
 * @since Mar 17, 2021
 */
@DefaultLocale("ru")
public interface ContentTemplateMessages extends Messages
{
    String addNew();

    String addTemplate();

    String addTemplateForm();

    String confirmDeleteTemplate(String templateTitle);

    String confirmMassDeleteTemplate();

    String creationTime();

    String editTemplateForm();

    String lastModificationTime();

    String contentSummary();

    String templateParameters();
}
