package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.dispatch.MoveLeftMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Команда смещения элемента левого меню вверх
 * <AUTHOR>
 * @since 19.06.2020
 */
public class MoveLeftMenuItemUpCommand extends MoveMenuItemUpCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "moveLeftMenuItemUp";

    @Inject
    public MoveLeftMenuItemUpCommand(@Assisted NavigationSettingsLMCommandParam param)
    {
        super(param);
    }

    @Override
    protected MoveNavigationMenuItemAction getAction()
    {
        return new MoveLeftMenuItemAction();
    }
}
