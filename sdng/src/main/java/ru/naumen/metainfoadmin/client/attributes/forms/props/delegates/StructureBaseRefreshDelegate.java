package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import java.util.Collection;
import java.util.HashSet;
import java.util.stream.Collectors;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.GetStructuredObjectsViewAction;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Базовая реализация {@link AttributeFormPropertyDelegateRefresh} для полей,
 * в которых есть возможность выбрать структуру.
 *
 * <AUTHOR>
 * @since 19.03.2025
 */
public abstract class StructureBaseRefreshDelegate<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    /**
     * {@link AsyncCallback} для обработки результата выполнения {@link GetStructuredObjectsViewAction}.
     *
     * <AUTHOR>
     * @since 19.03.2025
     */
    private static class SimpleResultBasicCallback extends BasicCallback<SimpleResult<Collection<DtObject>>>
    {
        private final ListBoxProperty property;
        private final AsyncCallback<Boolean> callback;

        public SimpleResultBasicCallback(PropertyContainerContext context,
                ListBoxProperty property, AsyncCallback<Boolean> callback)
        {
            super(context.getDisplay());
            this.property = property;
            this.callback = callback;
        }

        @Override
        protected void handleSuccess(SimpleResult<Collection<DtObject>> result)
        {
            property.getValueWidget().clear();
            property.getValueWidget().setHasEmptyOption(true);
            result.get().stream()
                    .sorted(ITitled.COMPARATOR)
                    .forEach(item -> property.getValueWidget().addItem(item.getTitle(), item.getUUID()));
            callback.onSuccess(true);
        }
    }

    private final DispatchAsync dispatch;

    @Inject
    protected StructureBaseRefreshDelegate(DispatchAsync dispatch)
    {
        this.dispatch = dispatch;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (!isShowStructuredObjectsViewField(context))
        {
            callback.onSuccess(false);
            return;
        }
        Collection<DtObject> permittedTypes = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.PERMITTED_TYPES);
        if (CollectionUtils.isEmpty(permittedTypes)
            || Constants.NOONE.equals(permittedTypes.iterator().next().getMetainfo()))
        {
            permittedTypes = new HashSet<>();
        }
        dispatch.execute(new GetStructuredObjectsViewAction(permittedTypes.stream()
                        .map(DtObject::getMetainfo)
                        .collect(Collectors.toList())),
                new SimpleResultBasicCallback(context, property, callback));
    }

    /**
     * Необходимо ли отобразить поле, содержащее структуру.
     * @return true, если поле нужно отобразить, иначе - false
     */
    protected abstract boolean isShowStructuredObjectsViewField(PropertyContainerContext context);
}