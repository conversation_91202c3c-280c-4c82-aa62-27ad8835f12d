package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.FieldHintUtils;
import ru.naumen.core.client.widgets.properties.IntegerBoxInfoProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;

/**
 * Делегат для подстроения свойства "ограничение количества знаков после запятой"
 * @param <F>
 *
 * <AUTHOR>
 * @since 10.07.2019
 */
public class DigitsCountRestrictionBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<Long, IntegerBoxInfoProperty>
{
    @Inject
    private AdminDialogMessages dialogMessages;
    @Inject
    private CommonMessages commonMessages;

    @Override
    public void bindProperty(final PropertyContainerContext context, IntegerBoxInfoProperty property,
            AsyncCallback<Void> callback)
    {
        property.setInfo(dialogMessages.helpDigitsCountRestriction());
        FieldHintUtils.showHint(property.getValueWidget(), commonMessages
                .digitsRestrictionPlaceholder());
        callback.onSuccess(null);
    }
}