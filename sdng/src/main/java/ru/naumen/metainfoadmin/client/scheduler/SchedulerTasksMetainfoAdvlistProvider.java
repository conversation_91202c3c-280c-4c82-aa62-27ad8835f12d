package ru.naumen.metainfoadmin.client.scheduler;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Maps;

import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.SchedulerTask;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Presentation_SnapshotObject;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка задач планировщика
 * <AUTHOR>
 * @since 23.10.2017
 */
public class SchedulerTasksMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Inject
    private SchedulerTaskMessages messages;

    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = Maps.newHashMap(super.createAttributes());
        attrs.put(SchedulerTask.Attributes.ATTR_TYPE_NAME.toString(),
                createTypeAttr(SchedulerTask.Attributes.ATTR_TYPE_NAME, messages.taskType()));
        attrs.put(SchedulerTask.Attributes.ATTR_PLANE_DATE.toString(),
                createDateTimeAttr(SchedulerTask.Attributes.ATTR_PLANE_DATE, messages.planExecutionDate()));
        attrCodes.add(SchedulerTask.Attributes.ATTR_TYPE_NAME.toString());
        attrCodes.add(SchedulerTask.Attributes.ATTR_PLANE_DATE.toString());
        return attrs;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return SchedulerTask.Attributes.ATTR_TITLE;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return SchedulerTask.Attributes.ATTR_CODE;
    }

    private Attribute createTypeAttr(AttributeFqn attrFqn, String title)
    {
        Attribute_SnapshotObject attr = (Attribute_SnapshotObject)createStringAttr(attrFqn, title);
        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.SCHEDULER_TASK_TYPE_EDIT);
        attr.__init__editPresentation(editPresentation);
        return attr;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return SchedulerTask.FQN;
    }
}
