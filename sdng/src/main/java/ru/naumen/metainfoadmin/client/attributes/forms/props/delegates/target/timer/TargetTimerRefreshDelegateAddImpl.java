package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class TargetTimerRefreshDelegateAddImpl<F extends ObjectForm> extends TargetTimerRefreshDelegateImpl<F>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean isBackTimer = BackTimerAttributeType.CODE.equals(attrType);
        MetaClass metainfo = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        metainfoService.getTimerDefinitions(isBackTimer, metainfo.getFqn(),
                new RefreshCallback(context, callback, property, null));
    }
}
