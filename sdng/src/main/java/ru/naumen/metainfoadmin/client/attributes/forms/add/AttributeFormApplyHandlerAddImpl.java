package ru.naumen.metainfoadmin.client.attributes.forms.add;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.events.ApplyFormEvent;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormApplyHandlerImpl;

/**
 * Обработчик нажатия на ОК формы добавления атрибута
 * <AUTHOR>
 * @since 01.06.2012
 */
public class AttributeFormApplyHandlerAddImpl extends AttributeFormApplyHandlerImpl<ObjectFormAdd>
{
    @Inject
    public AttributeFormApplyHandlerAddImpl(@Assisted("contextProps") IProperties contextProps,
            @Assisted("propertyValues") IProperties propertyValues,
            @Assisted PropertyContainerPresenter propertyContainer, @Assisted Context context,
            @Assisted Presenter formPresenter, @Assisted AsyncCallback<MetaClass> callback)
    {
        super(contextProps, propertyValues, propertyContainer, context, formPresenter, callback);
    }

    @Override
    public void onApplyForm(ApplyFormEvent event)
    {
        modifyAttribute();
    }
}
