package ru.naumen.metainfoadmin.client.scheduler.forms;

import java.util.Map.Entry;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.ShedulerCodeValidator;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskTypeFactory.SchedulerTaskType;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTasksPresenterSettings;

/**
 * <AUTHOR>
 * @since 02.06.2011
 *
 */
public class AddSchedulerTaskFormPresenter extends SchedulerTaskFormPresenterImpl
{
    @Inject
    private SchedulerTasksPresenterSettings settings;
    @Inject
    private ShedulerCodeValidator schedulerCodeValidator;

    @Inject
    public AddSchedulerTaskFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected boolean isNewSchedulerTask()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        validation.validate(code, schedulerCodeValidator);
        getDisplay().setCaptionText(messages.addingSchedulerTask());
        initTypes();
        getDisplay().setFixed(false);
        getDisplay().display();
    }

    private void initTypes()
    {
        SingleSelectCellList<?> selList = type.getValueWidget();
        for (Entry<String, SchedulerTaskType<?>> taskType : factory.getSchedulerTaskTypes().entrySet())
        {
            if (settings.containsType(taskType.getKey()))
            {
                selList.addItem(taskType.getValue().getTitle(), taskType.getKey());
            }
        }
    }
}
