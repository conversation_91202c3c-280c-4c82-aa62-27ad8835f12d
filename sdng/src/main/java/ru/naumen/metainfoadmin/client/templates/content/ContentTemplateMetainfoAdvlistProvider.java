package ru.naumen.metainfoadmin.client.templates.content;

import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate.Attributes;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка шаблонов контентов.
 * <AUTHOR>
 * @since Mar 15, 2021
 */
public class ContentTemplateMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Inject
    private ContentTemplateMessages messages;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private AdminDialogMessages dialogMessages;

    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attributes = super.createAttributes();
        attributes.put(Attributes.METACLASS.toString(),
                createLinkedClassesAttr(Attributes.METACLASS, commonMessages.clazz()));
        attributes.put(Attributes.SUMMARY.toString(),
                createStringAttr(Attributes.SUMMARY, messages.contentSummary()));
        attributes.put(Attributes.PROFILES.toString(),
                createStringAttr(Attributes.PROFILES, dialogMessages.contentProfiles()));
        attributes.put(Attributes.CREATION_TIME.toString(),
                createDateTimeAttr(Attributes.CREATION_TIME, messages.creationTime()));
        attributes.put(Attributes.LAST_MODIFICATION_TIME.toString(),
                createDateTimeAttr(Attributes.LAST_MODIFICATION_TIME, messages.lastModificationTime()));
        attrCodes.add(Attributes.METACLASS.toString());
        attrCodes.add(Attributes.SUMMARY.toString());
        attrCodes.add(Attributes.PROFILES.toString());
        attrCodes.add(Attributes.CREATION_TIME.toString());
        attrCodes.add(Attributes.LAST_MODIFICATION_TIME.toString());
        return attributes;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return Attributes.CODE;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return Attributes.TITLE;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return ContentTemplate.FQN;
    }
}
