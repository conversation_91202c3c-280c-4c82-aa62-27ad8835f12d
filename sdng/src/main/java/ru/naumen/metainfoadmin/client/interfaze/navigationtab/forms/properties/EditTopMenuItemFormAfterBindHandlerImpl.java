package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.inject.Singleton;

import ru.naumen.core.shared.navigationsettings.MenuItem;

/**
 * Хэндлер после биндинга формы редактирования элемента верхнего меню
 * <AUTHOR>
 * @since 01.07.2020
 */
@Singleton
public class EditTopMenuItemFormAfterBindHandlerImpl extends EditMenuItemFormAfterBindHandlerImpl<MenuItem>
{
}