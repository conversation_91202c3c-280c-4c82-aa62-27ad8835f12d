package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInDateTimeRestriction;

/**
 * Фабрика текстового представления места использования атрибута в ограничениях значения атрибутов "Дата" и "Дата/время"
 * <AUTHOR>
 * @since 6 февр. 2019 г.
 */
@Singleton
public class AttributeUsageInDateTimeRestrictionHtmlFactoryImpl extends
        AttributeHtmlFactoryImpl<AttributeUsageInDateTimeRestriction>
{

    private final AttributesMessages attributesMessages;

    @Inject
    public AttributeUsageInDateTimeRestrictionHtmlFactoryImpl(AttributesMessages attributesMessages)
    {
        this.attributesMessages = attributesMessages;
    }

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInDateTimeRestriction usagePlace)
    {
        return SafeHtmlUtils.fromString(attributesMessages.attributeUsageRestrictionPlace(usagePlace.getTitle()));
    }

}
