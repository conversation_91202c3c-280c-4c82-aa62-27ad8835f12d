package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Делегат изменения его значения для виджета {@link MenuItemPropertyCode#USE_ATTR_TITLE}
 *
 * <AUTHOR>
 * @since 12.03.2022
 */
public class UseAttrTitleLinkValuePropertyVCHImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        boolean useAttrTitle =
                Boolean.TRUE.equals(context.getPropertyValues().getProperty(MenuItemPropertyCode.USE_ATTR_TITLE));
        context.setProperty(useAttrTitle ? MenuItemPropertyCode.TITLE : MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE,
                null);
        context.getPropertyControllers().get(MenuItemPropertyCode.USE_ATTR_TITLE).refresh();
        context.getPropertyControllers().get(MenuItemPropertyCode.TITLE).refresh();
        context.getPropertyControllers().get(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE).refresh();
    }
}