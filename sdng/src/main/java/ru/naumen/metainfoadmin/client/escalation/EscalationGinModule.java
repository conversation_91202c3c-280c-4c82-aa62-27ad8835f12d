package ru.naumen.metainfoadmin.client.escalation;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Singleton;

import ru.naumen.metainfoadmin.client.escalation.actions.EscalationActionsGinjector;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeGinjector;
import ru.naumen.metainfoadmin.client.escalation.schemes.EscalationSchemesGinjector;
import ru.naumen.metainfoadmin.client.escalation.vmap.EscalationVMapGinjector;

/**
 * <AUTHOR>
 * @since 20.07.2012
 *
 */
public class EscalationGinModule extends AbstractGinModule
{
    public interface EscalationPlaceTabs
    {
        String ACTIONS = "actions";
        String SCHEMES = "schemes";
        String VMAPS = "valueMaps";
    }

    @Override
    protected void configure()
    {
        bind(EscalationSchemesGinjector.class).to(EscalationGinjector.class).in(Singleton.class);
        bind(EscalationActionsGinjector.class).to(EscalationGinjector.class).in(Singleton.class);
        bind(EscalationVMapGinjector.class).to(EscalationGinjector.class).in(Singleton.class);
        bind(EscalationSchemeGinjector.class).to(EscalationGinjector.class).in(Singleton.class);

        bind(EscalationMessages.class).in(Singleton.class);
        bind(EscalationPresenter.class);
    }
}
