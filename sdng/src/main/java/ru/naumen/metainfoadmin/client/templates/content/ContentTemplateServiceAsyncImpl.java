package ru.naumen.metainfoadmin.client.templates.content;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.templates.content.dispatch.AddContentTemplateAction;
import ru.naumen.metainfo.shared.templates.content.dispatch.DeleteContentTemplatesAction;
import ru.naumen.metainfo.shared.templates.content.dispatch.GetContentTemplateAction;
import ru.naumen.metainfo.shared.templates.content.dispatch.GetContentTemplatesAction;
import ru.naumen.metainfo.shared.templates.content.dispatch.QuickAddContentTemplateAction;
import ru.naumen.metainfo.shared.templates.content.dispatch.SaveContentTemplateAction;

/**
 * Реализация сервиса для работы с шаблонами контентов.
 * <AUTHOR>
 * @since Mar 24, 2021
 */
@Singleton
public class ContentTemplateServiceAsyncImpl implements ContentTemplateServiceAsync
{
    private final DispatchAsync dispatch;

    @Inject
    public ContentTemplateServiceAsyncImpl(DispatchAsync dispatch)
    {
        this.dispatch = dispatch;
    }

    @Override
    public void addContentTemplate(ContentTemplate template, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new AddContentTemplateAction(template), new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    public void addContentTemplate(String title, String code, IProperties properties, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new QuickAddContentTemplateAction(title, code, properties),
                new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    public void deleteContentTemplates(Collection<String> codes, AsyncCallback<Void> callback)
    {
        dispatch.execute(new DeleteContentTemplatesAction(new ArrayList<>(codes)),
                new BasicCallback<SimpleResult<List<String>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<List<String>> value)
                    {
                        if (!value.get().isEmpty())
                        {
                            callback.onFailure(new FxException(String.join("\n", value.get())));
                        }
                        else
                        {
                            callback.onSuccess(null);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t)
                    {
                        callback.onFailure(t);
                    }
                });
    }

    @Override
    public void loadContentTemplate(String code, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new GetContentTemplateAction(code), new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    public void loadContentTemplates(IProperties properties, AsyncCallback<List<DtObject>> callback)
    {
        dispatch.execute(new GetContentTemplatesAction(properties), new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    public void saveContentTemplate(ContentTemplate template, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new SaveContentTemplateAction(template), new SimpleResultCallbackDecorator<>(callback));
    }
}
