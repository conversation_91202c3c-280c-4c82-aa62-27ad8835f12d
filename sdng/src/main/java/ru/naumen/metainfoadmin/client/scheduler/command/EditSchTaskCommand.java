package ru.naumen.metainfoadmin.client.scheduler.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.scheduler.forms.EditSchedulerTaskFormPresenter;

/**
 * Команда редактирования задачи планировщика.
 * <AUTHOR>
 */
public class EditSchTaskCommand extends BaseCommandImpl<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>>
{
    @Inject
    protected SchedulerGinjector injector;

    @Inject
    public EditSchTaskCommand(@Assisted CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>> param)
    {
        EditSchedulerTaskFormPresenter form = injector.editSchedulerTaskFormPresenter();
        form.init(param.getValue().get(), param.getCallback());
        form.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
