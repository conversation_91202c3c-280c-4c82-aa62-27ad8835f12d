package ru.naumen.metainfoadmin.client.attributes.forms.props;

import static ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerGinModuleFactory.createSyncFactory;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.validation.TemplateValidator;
import ru.naumen.core.client.validation.TemplateValidatorFactory;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegatesGinModule;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;

/**
 * <AUTHOR>
 * @since 12.05.2012
 */
public class AttributeFormPropertiesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new AttributeFormPropertyDelegatesGinModule());
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(TemplateValidator.class, TemplateValidator.class)
            .build(TemplateValidatorFactory.class));

        install(createSyncFactory(new TypeLiteral<Collection<SelectItem>>(){}.getType(), TagsProperty.class));
        //@formatter:on
    }
}
