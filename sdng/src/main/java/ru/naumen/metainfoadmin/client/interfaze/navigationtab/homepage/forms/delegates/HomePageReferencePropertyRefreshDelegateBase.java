package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.homepage.HomePageType;

/**
 * Базовый делегат обновления свойств-ссылок элемента домашней страницы.
 * <AUTHOR>
 * @since 15.01.2023
 */
public abstract class HomePageReferencePropertyRefreshDelegateBase<T, P extends Property<T>> implements PropertyDelegateRefresh<T, P>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, P property, AsyncCallback<Boolean> callback)
    {
        boolean isReference = isReference(context);
        callback.onSuccess(isReference);
        if (isReference)
        {
            return;
        }
        context.getPropertyControllers().get(ReferenceCode.OBJECT_CLASS).unbindValidators();
        context.getPropertyControllers().get(ReferenceCode.OBJECT_CASES).unbindValidators();
        context.getPropertyControllers().get(ReferenceCode.ATTRIBUTE_CHAIN).unbindValidators();
        context.getPropertyControllers().get(HomePage.REFERENCE_CARD_TYPE).unbindValidators();
    }

    /**
     * @param context контейнер свойств формы
     * @return true, если "Вид домашней страницы" - {@link HomePageType#REFERENCE ссылка на карточку}
     */
    public static boolean isReference(PropertyContainerContext context)
    {
        String typeStr = context.getPropertyValues().getProperty(HomePage.TYPE);
        HomePageType type = HomePageType.valueOf(typeStr);
        return HomePageType.REFERENCE.equals(type);
    }
}