package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.inject.Inject;

import com.google.gwt.http.client.UrlBuilder;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.validation.CustomLinkValidator;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Логика обновления свойства "Адрес ссылки" для "Произвольной ссылки".
 * <AUTHOR>
 * @since 26 сен. 2021 г.
 */
public class CustomLinkValuePropertyDelegateRefreshImpl implements PropertyDelegateRefresh<String, TextBoxProperty>
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    private CustomLinkValidator validator;

    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);

        boolean isCustomLink = MenuItemType.customLink.equals(type);
        if (Boolean.TRUE.equals(context.getPropertyValues().getProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE)))
        {
            UrlBuilder builder = new UrlBuilder().setProtocol(Location.getProtocol()).setHost(Location.getHost());
            property.setDescription(cmessages.afterSystemUrl() + " " + builder.buildString());
            validator.setStartsWithSystemUrl(true);
        }
        else
        {
            property.setDescription("");
            validator.setStartsWithSystemUrl(false);
        }
        callback.onSuccess(isCustomLink);
        if (!isCustomLink)
        {
            context.getPropertyControllers().get(MenuItemPropertyCode.CUSTOM_LINK_VALUE).unbindValidators();
        }
    }
}