package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode.REFERENCE_UI_TEMPLATE;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.templates.ui.UITemplateDto;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;

/**
 * Логика обновления свойства «Шаблон» для ссылок на карточки.
 * <AUTHOR>
 * @since Aug 12, 2021
 */
public class ReferenceTemplateRefreshDelegateImpl
        implements PropertyDelegateRefresh<SelectItem, ListBoxWithEmptyOptProperty>
{
    private final UITemplateMetainfoServiceAsync templateMetainfoService;

    @Inject
    public ReferenceTemplateRefreshDelegateImpl(UITemplateMetainfoServiceAsync templateMetainfoService)
    {
        this.templateMetainfoService = templateMetainfoService;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        boolean isReference = MenuItemType.reference.name().equals(typeStr);
        boolean isLeftMenu = context.getPropertyValues().hasProperty(MenuSettingsPropertyCode.PRESENTATION);
        callback.onSuccess(isLeftMenu && isReference);

        DtObject referenceDto = context.getPropertyValues().getProperty(ReferenceCode.REFERENCE_VALUE);
        if (null == referenceDto)
        {
            updateProperty(Collections.emptyList(), property, context);
            return;
        }

        List<String> tabs = referenceDto.getProperty(ReferenceCode.TAB_UUIDS);
        ClassFqn classFqn = referenceDto.getProperty(ReferenceCode.CLASS_FQN);
        if (null == classFqn)
        {
            updateProperty(Collections.emptyList(), property, context);
            return;
        }

        if (CollectionUtils.isEmpty(tabs))
        {
            templateMetainfoService.loadAvailableTemplates(classFqn, UI.WINDOW_KEY, null,
                    new BasicCallback<List<UITemplateDto>>()
                    {
                        @Override
                        protected void handleSuccess(List<UITemplateDto> templates)
                        {
                            updateProperty(templates, property, context);
                        }
                    });
            return;
        }

        Reference refContent = context.getPropertyValues().getProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE);
        List<String> allTabs = new ArrayList<>(tabs);
        if (refContent != null && refContent.getTabUUIDs() != null)
        {
            allTabs.addAll(refContent.getTabUUIDs());
        }

        templateMetainfoService.loadAvailableTemplates(classFqn, UI.WINDOW_KEY, allTabs.get(allTabs.size() - 1),
                new BasicCallback<List<UITemplateDto>>()
                {
                    @Override
                    protected void handleSuccess(List<UITemplateDto> templates)
                    {
                        updateProperty(templates, property, context);
                    }
                });
    }

    private void updateProperty(List<UITemplateDto> templates, ListBoxWithEmptyOptProperty property,
            PropertyContainerContext context)
    {
        property.getValueWidget().clear();
        property.getValueWidget().refreshPopupCellList();
        String currentValue = context.getPropertyValues().getProperty(REFERENCE_UI_TEMPLATE);
        boolean hasValue = false;
        for (UITemplateDto templateDto : templates)
        {
            property.getValueWidget().addItem(templateDto.getTitle(), templateDto.getCode());
            if (currentValue != null && currentValue.equals(templateDto.getCode()))
            {
                hasValue = true;
            }
        }
        if (hasValue)
        {
            property.trySetObjValue(currentValue, true);
        }
        else
        {
            property.clearValue();
        }
    }
}
