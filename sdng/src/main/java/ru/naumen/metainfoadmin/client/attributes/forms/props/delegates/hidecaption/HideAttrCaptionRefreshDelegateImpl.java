package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidecaption;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.HIDDEN_ATTR_CAPTION;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.INHERIT;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Делегат, определяющий видимость свойства "Скрывать название атрибута"
 * <AUTHOR>
 * @since 02.03.2020
 */
public class HideAttrCaptionRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isInherit = !Boolean.TRUE.equals(context.getPropertyValues().getProperty(INHERIT));
        context.setPropertyEnabled(HIDDEN_ATTR_CAPTION, isInherit);
    }
}
