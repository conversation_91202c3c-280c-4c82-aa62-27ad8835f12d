package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.TopMenuItemFormPresenter;

/**
 * Команда добавления элемента верхнего меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class AddTopMenuItemCommand extends AddMenuItemCommand<MenuItem>
{
    public static final String ID = "addTopMenuItemCommand";

    @Inject
    public AddTopMenuItemCommand(@Assisted NavigationSettingsTMCommandParam param,
            Provider<TopMenuItemFormPresenter<ObjectFormAdd>> addMenuItemFormProvider)
    {
        super(param, addMenuItemFormProvider);
    }
}
