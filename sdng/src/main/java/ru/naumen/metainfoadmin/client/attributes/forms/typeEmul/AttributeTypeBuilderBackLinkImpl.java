package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import java.util.Collection;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.DirectLinkValueFormatter;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
@Singleton
public class AttributeTypeBuilderBackLinkImpl implements AttributeTypeBuilder
{
    @Inject
    DirectLinkValueFormatter valueFormatter;

    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        String value = propertyValues.getProperty(AttributeFormPropertyCode.DIRECT_LINK_TARGET);
        if (!StringUtilities.isEmpty(value))
        {
            type.setProperty(ObjectAttributeType.METACLASS_FQN, valueFormatter.strFqnFrom(value));
            type.setProperty(BackLinkAttributeType.BACK_ATTR_CODE, valueFormatter.attrCodeFrom(value));
        }
        Collection<DtObject> permittedTypes = propertyValues.getProperty(AttributeFormPropertyCode.PERMITTED_TYPES);
        Collection<ClassFqn> permittedTypesFqns = Collections2.transform(permittedTypes, DtObject.FQN_EXTRACTOR);
        type.setProperty(ObjectAttributeType.PERMITTED_TYPES, Sets.newHashSet(permittedTypesFqns));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        String classFqn = type.getProperty(ObjectAttributeType.METACLASS_FQN);
        String backAttrCode = type.getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
        propertyValues.setProperty(AttributeFormPropertyCode.DIRECT_LINK_TARGET,
                valueFormatter.to(classFqn, backAttrCode));
    }
}
