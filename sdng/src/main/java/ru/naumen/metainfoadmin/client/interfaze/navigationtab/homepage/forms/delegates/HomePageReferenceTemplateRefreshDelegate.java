package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfo.shared.templates.ui.UITemplateDto;

/**
 * Делегат обновления свойства "Шаблон карточки" элемента домашней страницы {@link HomePage#REFERENCE_UI_TEMPLATE}
 * <AUTHOR>
 * @since 15.01.2023
 */
public class HomePageReferenceTemplateRefreshDelegate
        implements PropertyDelegateRefresh<SelectItem, ListBoxWithEmptyOptProperty>
{
    private final UITemplateMetainfoServiceAsync templateMetainfoService;

    @Inject
    public HomePageReferenceTemplateRefreshDelegate(UITemplateMetainfoServiceAsync templateMetainfoService)
    {
        this.templateMetainfoService = templateMetainfoService;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(HomePage.TYPE);
        boolean isReference = HomePageType.REFERENCE.name().equals(typeStr);
        callback.onSuccess(isReference);

        DtObject referenceDto = context.getPropertyValues().getProperty(ReferenceCode.REFERENCE_VALUE);
        if (null == referenceDto)
        {
            updateProperty(Collections.emptyList(), property, context);
            return;
        }

        List<String> tabs = referenceDto.getProperty(ReferenceCode.TAB_UUIDS);
        ClassFqn classFqn = referenceDto.getProperty(ReferenceCode.CLASS_FQN);
        if (null == classFqn)
        {
            updateProperty(Collections.emptyList(), property, context);
            return;
        }

        if (CollectionUtils.isEmpty(tabs))
        {
            templateMetainfoService.loadAvailableTemplates(classFqn, UI.WINDOW_KEY, null,
                    new BasicCallback<List<UITemplateDto>>()
                    {
                        @Override
                        protected void handleSuccess(List<UITemplateDto> templates)
                        {
                            updateProperty(templates, property, context);
                        }
                    });
            return;
        }

        Reference refContent = context.getPropertyValues().getProperty(HomePage.REFERENCE_TAB_VALUE);
        List<String> allTabs = new ArrayList<>(tabs);
        if (refContent != null && refContent.getTabUUIDs() != null)
        {
            allTabs.addAll(refContent.getTabUUIDs());
        }

        templateMetainfoService.loadAvailableTemplates(classFqn, UI.WINDOW_KEY, allTabs.get(allTabs.size() - 1),
                new BasicCallback<List<UITemplateDto>>()
                {
                    @Override
                    protected void handleSuccess(List<UITemplateDto> templates)
                    {
                        updateProperty(templates, property, context);
                    }
                });
    }

    private void updateProperty(List<UITemplateDto> templates, ListBoxWithEmptyOptProperty property,
            PropertyContainerContext context)
    {
        property.getValueWidget().clear();
        property.getValueWidget().refreshPopupCellList();
        String currentValue = context.getPropertyValues().getProperty(HomePage.REFERENCE_UI_TEMPLATE);
        for (UITemplateDto templateDto : templates)
        {
            property.getValueWidget().addItem(templateDto.getTitle(), templateDto.getCode());
            if (currentValue != null && currentValue.equals(templateDto.getCode()))
            {
                property.trySetObjValue(currentValue, true);
                return;
            }
        }
        property.clearValue();
    }
}
