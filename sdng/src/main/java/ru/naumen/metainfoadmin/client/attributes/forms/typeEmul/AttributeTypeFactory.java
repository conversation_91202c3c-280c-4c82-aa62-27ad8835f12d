package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.elements.AttributeType;

import com.google.inject.ImplementedBy;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
@ImplementedBy(AttributeTypeFactoryImpl.class)
public interface AttributeTypeFactory
{
    AttributeType create(IProperties contextProps, IProperties propertyValues);

    void invert(AttributeType attrType, IProperties propertyValues);
}
