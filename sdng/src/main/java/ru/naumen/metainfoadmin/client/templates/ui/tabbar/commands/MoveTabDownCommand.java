package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;

/**
 * Команда перемещения вкладки вниз.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class MoveTabDownCommand extends MoveTabCommand
{
    @Inject
    public MoveTabDownCommand(@Assisted TabModelCommandParam param)
    {
        super(param, 1);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DOWN;
    }
}
