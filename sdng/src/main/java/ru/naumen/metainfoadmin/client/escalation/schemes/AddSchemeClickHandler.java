package ru.naumen.metainfoadmin.client.escalation.schemes;

import ru.naumen.metainfoadmin.client.escalation.schemes.forms.AddEscalationSchemeForm;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormFactory;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class AddSchemeClickHandler implements ClickHandler
{
    private final AddEscalationSchemeForm addForm;

    @Inject
    public AddSchemeClickHandler(@Assisted EventBus presenterEventBus,
            EscalationSchemeFormFactory<AddEscalationSchemeForm> formFactory)
    {
        addForm = formFactory.create(presenterEventBus);
    }

    @Override
    public void onClick(ClickEvent event)
    {
        addForm.bind();
    }
}
