package ru.naumen.metainfoadmin.client.scheduler.forms;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;

/**
 * <AUTHOR>
 * @since 02.06.2011
 *
 */
public class EditSchedulerTaskFormPresenter extends SchedulerTaskFormPresenterImpl
{
    @Inject
    public EditSchedulerTaskFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected boolean isNewSchedulerTask()
    {
        return false;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingSchedulerTask());
        SingleSelectCellList<?> selList = type.getValueWidget();
        selList.addItem(factory.getTypeTitle(schTask.getType()), schTask.getType());
        type.setDisable();
        ((ListBoxWithEmptyOptProperty)type).trySetObjValue(schTask.getType());

        code.setDisable();
        code.setValue(schTask.getCodeOnForm());
        getDisplay().display();
    }
}
