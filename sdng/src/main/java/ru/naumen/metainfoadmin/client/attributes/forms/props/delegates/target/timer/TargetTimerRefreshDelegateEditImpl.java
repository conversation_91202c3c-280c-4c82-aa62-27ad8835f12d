package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class TargetTimerRefreshDelegateEditImpl extends TargetTimerRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        final String value = attribute.getType().<ru.naumen.metainfo.shared.elements.TimerAttributeType> cast()
                .getTimerDefinitionCode();
        boolean isBackTimer = BackTimerAttributeType.CODE.equals(attribute.getType().getCode());
        metainfoService.getTimerDefinitions(isBackTimer, attribute.getMetaClassLite().getFqn(), new RefreshCallback(
                context, callback, property, value));
    }
}
