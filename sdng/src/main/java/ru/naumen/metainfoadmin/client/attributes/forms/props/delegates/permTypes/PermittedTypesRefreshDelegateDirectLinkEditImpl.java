package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Collection;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;

import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetDescendantMetaClassesLiteAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyController;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Обновление значения свойства "Ограничение по типам" на форме редактирования атрибута
 * <AUTHOR>
 * @since 28.05.2012
 */
public class PermittedTypesRefreshDelegateDirectLinkEditImpl implements PermittedTypesRefreshDelegate<ObjectFormEdit>
{
    @Inject
    DispatchAsync dispatch;
    @Inject
    GetPermittedRelatedTypesCallbackFactory callbackFactory;
    @Inject
    AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(
            final PropertyContainerContext context,
            PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    HierarchicalMultiSelectionModel<DtObject>>> property,
            final AsyncCallback<Boolean> callback)
    {
        final Attribute attr = context.getContextValues().<Attribute> getProperty(AttributeFormContextValues.ATTRIBUTE);
        if (!attr.isSystemEditable())
        {
            callback.onSuccess(false);
        }
        ClassFqn metaclassFqn = context.getContextValues().<MetaClass> getProperty(AttributeFormContextValues.METAINFO)
                .getFqn();
        if (Constants.AbstractStateResponsibleEvent.FQN.equals(metaclassFqn))
        {
            return;
        }
        GetDescendantMetaClassesLiteAction getDescendantsAction = new GetDescendantMetaClassesLiteAction(
                PermittedTypesPropertyController.PERMITTED_TYPES_CLASS_FQN_EXTRACTOR.apply(context), true);
        GetPermittedRelatedTypesAction getPermittedTypesAction = new GetPermittedRelatedTypesAction(metaclassFqn,
                attr.getCode());
        BatchAction batch = new BatchAction(OnException.ROLLBACK, getDescendantsAction, getPermittedTypesAction);
        dispatch.execute(batch, callbackFactory.create(context, callback));
    }
}
