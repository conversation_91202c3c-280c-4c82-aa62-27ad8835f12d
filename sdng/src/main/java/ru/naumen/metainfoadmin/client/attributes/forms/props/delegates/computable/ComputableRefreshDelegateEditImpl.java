package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат, определяющий, должно ли отображаться свойство "Вычислимый" на форме редактирования атрибута или нет
 * <AUTHOR>
 * @since 10.07.2012
 *
 */
public class ComputableRefreshDelegateEditImpl extends ComputableRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    protected boolean isComputable(PropertyContainerContext context)
    {
        Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        boolean notAggregated = attr.isSystemEditable();
        Boolean exportNDAP = context.getPropertyValues().getProperty(AttributeFormPropertyCode.EXPORT_NDAP);
        return super.isComputable(context) && notAggregated && !exportNDAP;
    }
}
