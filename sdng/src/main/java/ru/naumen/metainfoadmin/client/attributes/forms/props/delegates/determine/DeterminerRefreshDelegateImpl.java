package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 */
public class DeterminerRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxWithEmptyOptProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        if (!determinable)
        {
            callback.onSuccess(false);
            return;
        }
        callback.onSuccess(true);
    }
}
