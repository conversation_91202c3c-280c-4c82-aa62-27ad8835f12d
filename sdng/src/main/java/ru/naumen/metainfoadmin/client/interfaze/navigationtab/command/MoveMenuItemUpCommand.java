package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.navigationsettings.IMenuItem;

/**
 * Смещение элемента меню вверх
 * <AUTHOR>
 * @since 27 сент. 2013 г.
 */
public abstract class MoveMenuItemUpCommand<M extends IMenuItem> extends MoveMenuItemCommand<M>
{
    public MoveMenuItemUpCommand(NavigationSettingsMenuItemAbstractCommandParam param)
    {
        super(param, -1);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UP;
    }

    @Override
    protected boolean isPossible(int index, int count)
    {
        return index > 0;
    }

}
