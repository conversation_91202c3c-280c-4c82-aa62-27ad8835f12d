package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.InfoDialogDisplayImpl;
import ru.naumen.core.client.listeditor.ListEditorResources;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.reference.TabBarReference;
import ru.naumen.metainfoadmin.client.dynadmin.ReloadUIEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateHelper;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateMessages;

/**
 * Представление формы управления вкладками.
 * <AUTHOR>
 * @since Aug 01, 2021
 */
public class ManageTabsFormPresenter extends BasicPresenter<InfoDialogDisplayImpl>
{
    private final UITemplateMessages templateMessages;
    private final TabListManagerPresenter managerPresenter;
    private final UITemplateMetainfoServiceAsync templateMetainfoService;
    private final UITemplateHelper templateHelper;
    private final TabManagementModelBuilder modelBuilder;
    private final TabManagementModelApplier modelApplier;

    private UIContext context;
    private TabBar content;
    private TabBarReference tabBarReference;

    @Inject
    public ManageTabsFormPresenter(InfoDialogDisplayImpl display, EventBus eventBus,
            UITemplateMessages templateMessages,
            ListEditorResources listEditorResources,
            TabListManagerPresenter managerPresenter,
            UITemplateMetainfoServiceAsync templateMetainfoService,
            UITemplateHelper templateHelper,
            TabManagementModelBuilder modelBuilder,
            TabManagementModelApplier modelApplier)
    {
        super(display, eventBus);
        this.templateMessages = templateMessages;
        this.managerPresenter = managerPresenter;
        this.templateMetainfoService = templateMetainfoService;
        this.templateHelper = templateHelper;
        this.modelBuilder = modelBuilder;
        this.modelApplier = modelApplier;
        listEditorResources.cellTableStyle().ensureInjected();
        display.asWidget().addStyleName(listEditorResources.cellTableStyle().editTabListDialogBox());
    }

    public void init(UIContext context, TabBar content)
    {
        this.context = context;
        UITemplateContext templateContext = Objects.requireNonNull(context.getUITemplateContext());
        this.tabBarReference = templateContext.getTemplateReference(content);
        this.content = templateContext.getOriginalContent(content);
    }

    @Override
    public void refreshDisplay()
    {
        managerPresenter.refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(templateMessages.manageTabs());

        registerHandler(getDisplay().getCloseButton().addClickHandler(event -> onClose()));
        getDisplay().display();

        managerPresenter.init(context);
        managerPresenter.getTabs().clear();
        managerPresenter.getTabs().addAll(modelBuilder.build(content, tabBarReference));
        managerPresenter.bind();
        getDisplay().addContent(managerPresenter.getDisplay());
    }

    @Override
    protected void onUnbind()
    {
        managerPresenter.unbind();
        super.onUnbind();
    }

    private void applyModel(List<TabModel> model, TabBarReference tabBarReference)
    {
        UITemplateContext templateContext = Objects.requireNonNull(context.getUITemplateContext());
        modelApplier.apply(model, tabBarReference, templateContext);
        templateMetainfoService.previewContent(templateContext.getTemplate(), context.getMetainfo().getFqn(),
                context.getCode(), new BasicCallback<Content>(context.getReadyState())
                {
                    @Override
                    protected void handleSuccess(Content content)
                    {
                        ContentInfo contentInfo = templateHelper.createContentInfo(context, content);
                        context.getEventBus().fireEvent(new ReloadUIEvent(contentInfo));
                    }
                });
    }

    private void onClose()
    {
        applyModel(managerPresenter.getTabs(), tabBarReference);
        unbind();
    }
}
