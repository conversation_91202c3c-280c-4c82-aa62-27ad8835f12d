package ru.naumen.metainfoadmin.client.attributes.forms.usage;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Gin-модуль для формы "Используется в настройках" в интерфейсе технолога
 * <AUTHOR>
 * @since 15 Jun 18
 */
public class ShowUsageGinModule extends AbstractGinModule
{
    public interface ShowUsageAttrPresenterFactory
    {
        AttributeUsagePresenter create(Context context, Attribute attribute);
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder().implement(Presenter.class, AttributeUsagePresenter.class).build(
                ShowUsageAttrPresenterFactory.class));
        //@formatter:on
    }
}