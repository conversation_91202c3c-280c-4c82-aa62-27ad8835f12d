package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.inject.Singleton;

/**
 * <AUTHOR>
 *
 */
@Singleton
public class AttributeTypeBuilderStringImpl implements AttributeTypeBuilder
{

    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(StringAttributeType.INPUT_MASK,
                propertyValues.getProperty(AttributeFormPropertyCode.INPUTMASK));
        type.setProperty(StringAttributeType.INPUT_MASK_MODE,
                propertyValues.getProperty(AttributeFormPropertyCode.INPUTMASK_MODE));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        String inputMaskMode = type.getProperty(StringAttributeType.INPUT_MASK_MODE);
        String inputMask = type.getProperty(StringAttributeType.INPUT_MASK);
        propertyValues.setProperty(AttributeFormPropertyCode.INPUTMASK, inputMask);
        propertyValues.setProperty(AttributeFormPropertyCode.INPUTMASK_MODE, inputMaskMode);
        if (StringAttributeType.INPUT_MASK_ALIAS.equals(inputMaskMode))
        {
            propertyValues.setProperty(AttributeFormPropertyCode.INPUTMASK_ALIAS, inputMask);
        }
        else if (StringAttributeType.INPUT_MASK_DEFINITIONS.equals(inputMaskMode))
        {
            propertyValues.setProperty(AttributeFormPropertyCode.INPUTMASK_DEFINITIONS, inputMask);
        }
        else if (StringAttributeType.INPUT_MASK_REGEX.equals(inputMaskMode))
        {
            propertyValues.setProperty(AttributeFormPropertyCode.INPUTMASK_REGEX, inputMask);
        }
    }
}