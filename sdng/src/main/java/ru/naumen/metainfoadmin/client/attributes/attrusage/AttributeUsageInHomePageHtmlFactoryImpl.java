package ru.naumen.metainfoadmin.client.attributes.attrusage;

import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.NavigationHomePagePlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInHomePage;

/**
 * Фабрика генерации ссылки на элемент домашней страницы типа "Ссылка на карточку" для "Атрибута связи"<br>
 * Предполагается использование в блоке "Места использования атрибута".
 *
 * <AUTHOR>
 * @since 8.02.2023
 */
public class AttributeUsageInHomePageHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInHomePage>
{
    @Inject
    private Formatters formatters;
    @Inject
    private PlaceHistoryMapper historyMapper;
    @Inject
    private NavigationSettingsMessages navMessages;

    @Override
    public SafeHtml create(@Nullable PresentationContext context, AttributeUsageInHomePage usage)
    {
        return new SafeHtmlBuilder()
                .append(formatters.formatHyperlinkAsHtml(createLink(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLink(AttributeUsageInHomePage usage)
    {
        String title = Objects.requireNonNull(usage.getTitle());
        String code = Objects.requireNonNull(usage.getCode());
        NavigationHomePagePlace place = new NavigationHomePagePlace(code);
        return new Hyperlink(navMessages.homePageCardName(title),
                StringUtilities.getHrefByToken(historyMapper.getToken(place)));
    }
}