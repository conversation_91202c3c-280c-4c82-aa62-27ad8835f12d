package ru.naumen.metainfoadmin.client.escalation.schemes;

import ru.naumen.metainfoadmin.client.escalation.schemes.columns.EscalationSchemesColumnsGinjector;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandsGinjector;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinjector;

import com.google.gwt.inject.client.GinModules;

/**
 * <AUTHOR>
 * @since 01.08.2012
 *
 */
@GinModules(EscalationSchemesGinModule.class)
public interface EscalationSchemesGinjector extends EscalationSchemesColumnsGinjector,
        EscalationSchemesCommandsGinjector, EscalationSchemesFormsGinjector
{

}
