package ru.naumen.metainfoadmin.client.templates.list.card;

import java.util.ArrayList;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.client.activity.HasTokenParts;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * {@link Place} карточки шаблона списка.
 * <AUTHOR>
 * @since 19.04.2018
 */
public class ListTemplatePlace extends Place implements HasTokenParts
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ListTemplatePlace>
    {
        @Override
        public ListTemplatePlace getPlace(String token)
        {
            return new ListTemplatePlace(token);
        }

        @Override
        public String getToken(ListTemplatePlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "listTemplate";

    private String code;

    public ListTemplatePlace()
    {
    }

    public ListTemplatePlace(String code)
    {
        this.code = code;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (!(obj instanceof ListTemplatePlace))
        {
            return false;
        }
        ListTemplatePlace other = (ListTemplatePlace)obj;
        return ObjectUtils.equals(other.code, code);
    }

    public String getCode()
    {
        return code;
    }

    @Override
    public ArrayList<Object> getTokenParts()
    {
        ArrayList<Object> tokens = new ArrayList<>();
        tokens.add(code);
        return tokens;
    }

    @Override
    public int hashCode()
    {
        return 31 + (code == null ? 0 : code.hashCode());
    }

    @Override
    public String toString()
    {
        return "ListTemplatePlace [" + code + "]";
    }
}
