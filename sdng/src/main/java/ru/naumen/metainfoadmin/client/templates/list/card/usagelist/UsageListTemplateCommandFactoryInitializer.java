package ru.naumen.metainfoadmin.client.templates.list.card.usagelist;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands.ApplyListTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands.BreakLinkListTemplateCommand;

/**
 * Инициализатор команд для списка мест использования шаблона списка
 * <AUTHOR>
 * @since 08.08.2018
 */
@Singleton
public class UsageListTemplateCommandFactoryInitializer
{
    @Inject
    public UsageListTemplateCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<ApplyListTemplateCommand, CommandParam<Collection<DtObject>, Void>> applyProvider,
            CommandProvider<BreakLinkListTemplateCommand, CommandParam<Collection<DtObject>, Void>> breakLinkProvider)
    {
        factory.register(UsageListTemplateCommandCode.APPLY, applyProvider);
        factory.register(UsageListTemplateCommandCode.BREAK_LINK, breakLinkProvider);
    }
}
