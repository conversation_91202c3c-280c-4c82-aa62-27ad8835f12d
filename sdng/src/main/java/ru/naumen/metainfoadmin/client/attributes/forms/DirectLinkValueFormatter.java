package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.DirectLinkAttributeDescription;

import com.google.inject.Singleton;

/**
 * Транслятор значения ссылки (пары код типа-код атрибута) в строку для хранения в AttributeType и обратно
 * <AUTHOR>
 * @since 24.05.2012
 */
@Singleton
public class DirectLinkValueFormatter
{
    public String attrCodeFrom(String value)
    {
        String[] values = value.split("#");
        return values[1];
    }

    public ClassFqn fqnFrom(String value)
    {
        return ClassFqn.parse(strFqnFrom(value));
    }

    public String strFqnFrom(String value)
    {
        String[] values = value.split("#");
        return values[0];
    }

    public String to(ClassFqn fqn, String attrCode)
    {
        return to(fqn.toString(), attrCode);
    }

    public String to(DirectLinkAttributeDescription attrDesc)
    {
        return to(attrDesc.getFqn(), attrDesc.getAttrCode());
    }

    public String to(String fqn, String attrCode)
    {
        return fqn + "#" + attrCode;
    }
}
