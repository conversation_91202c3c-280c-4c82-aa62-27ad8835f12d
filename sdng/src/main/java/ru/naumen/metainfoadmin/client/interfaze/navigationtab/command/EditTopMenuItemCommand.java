package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.TopMenuItemFormPresenter;

/**
 * Команда редактирования элемента верхнего меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class EditTopMenuItemCommand extends EditMenuItemCommand<MenuItem>
{
    public static final String ID = "editTopMenuItemCommand";

    @Inject
    public EditTopMenuItemCommand(@Assisted NavigationSettingsTMCommandParam param,
            Provider<TopMenuItemFormPresenter<ObjectFormEdit>> editMenuItemFormProvider)
    {
        super(param, editMenuItemFormProvider);
    }
}
