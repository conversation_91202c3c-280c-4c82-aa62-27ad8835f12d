package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.CODE;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyParametersDescriptorFactoryImpl;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class EditAttributeFormPropertyParametersDescriptorFactoryImpl extends
        AttributeFormPropertyParametersDescriptorFactoryImpl<ObjectFormEdit>
{
    @Override
    protected void build()
    {
        super.build();
        registerOrModifyProperty(CODE, cmessages.code(), false, "code", 3, true, true);
    }
}
