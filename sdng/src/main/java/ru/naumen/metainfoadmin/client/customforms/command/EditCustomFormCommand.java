package ru.naumen.metainfoadmin.client.customforms.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;
import ru.naumen.metainfoadmin.client.customforms.form.EditCustomFormFormPresenter;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
public class EditCustomFormCommand extends CustomFormPresenterCommandBase
{
    @Inject
    private final Provider<EditCustomFormFormPresenter> formProvider;

    @Inject
    // @formatter:off
    public EditCustomFormCommand(
            @Assisted ContentCommandParam<CustomForm, CustomForm> param,
            Provider<EditCustomFormFormPresenter> formProvider)
    // @formatter:on
    {
        super(param);
        this.formProvider = formProvider;
    }

    @Override
    public void execute(final CommandParam<CustomForm, CustomForm> param)
    {
        super.execute(new CommandParam<>(param.getValue(), param.getCallback()));
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<CustomForm, CustomForm> getPresenter(CustomForm value)
    {
        EditCustomFormFormPresenter presenter = formProvider.get();
        presenter.setMetaClass(this.metaClassUnderEdit);
        return presenter;
    }
}
