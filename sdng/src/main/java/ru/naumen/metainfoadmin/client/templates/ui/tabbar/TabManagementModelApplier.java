package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.metainfo.client.ui.template.creation.UITemplateCreationService;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.reference.TabBarReference;
import ru.naumen.metainfo.shared.ui.reference.TabReference;
import ru.naumen.metainfo.shared.ui.reference.UIComponentReference;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;

/**
 * Компонент для применения модели вкладок к шаблону.
 * <AUTHOR>
 * @since Aug 01, 2021
 */
@Singleton
public class TabManagementModelApplier
{
    private final MetainfoUtils metainfoUtils;
    private final UITemplateCreationService templateCreationService;

    @Inject
    public TabManagementModelApplier(MetainfoUtils metainfoUtils, UITemplateCreationService templateCreationService)
    {
        this.metainfoUtils = metainfoUtils;
        this.templateCreationService = templateCreationService;
    }

    public void apply(List<TabModel> model, TabBarReference tabBarReference, UITemplateContext templateContext)
    {
        tabBarReference.getTabs().clear();
        model.stream().skip(1).filter(TabModel::isEnabled).forEach(tabModel ->
        {
            TabReference tab = templateContext.getTemplateReference(tabModel.getCode());
            if (null != tab)
            {
                metainfoUtils.setLocalizedValue(tab.getCaption(), tabModel.getTitle());
                tabBarReference.getTabs().add(tab);
                tab.setParent(tabBarReference);
            }
            else
            {
                Content original = templateContext.getOriginalContent(tabModel.getCode());
                UIComponentReference reference = templateCreationService.create(original);
                if (reference instanceof TabReference)
                {
                    TabReference tabReference = (TabReference)reference;
                    tabBarReference.getTabs().add(tabReference);
                    tabReference.setParent(tabBarReference);
                }
            }
        });
        templateContext.refreshReferences();
    }
}
