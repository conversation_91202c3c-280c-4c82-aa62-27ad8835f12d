package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 *
 */
public class InputmaskModeRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean hasInputMask = Presentations.STRING_EDIT_WITH_MASK
                .equals(context.getPropertyValues().getProperty(AttributeFormPropertyCode.EDIT_PRS));
        if (!hasInputMask)
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK_MODE, "");
        }
        else
        {
            String inputMaskMode = SelectListPropertyValueExtractor.getValue(property);
            if (StringUtilities.isEmpty(inputMaskMode))
            {
                inputMaskMode = StringAttributeType.INPUT_MASK_ALIAS;
            }
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK_MODE,
                    inputMaskMode);
            property.trySetObjValue(inputMaskMode, false);
        }
        callback.onSuccess(hasInputMask);
    }
}