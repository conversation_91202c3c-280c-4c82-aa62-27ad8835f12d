package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.card.StructuredObjectsViewPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInStructuredObjectsViewItem;

/**
 * Представление для отображения значения места использования атрибута в элементе структуры на форме "Используется в 
 * настройках" в таблице атрибутов
 * <AUTHOR>
 * @since 13.01.2020
 */
@Singleton
public class AttributeUsageInStructuredObjectsViewItemHtmlFactoryImpl extends
        AttributeHtmlFactoryImpl<AttributeUsageInStructuredObjectsViewItem>
{
    @Inject
    private Formatters formatters;
    @Inject
    private PlaceHistoryMapper historyMapper;
    @Inject
    private StructuredObjectsViewsMessages structuredObjectsViewsMessages;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInStructuredObjectsViewItem usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLink(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLink(AttributeUsageInStructuredObjectsViewItem usage)
    {
        StructuredObjectsViewPlace place = new StructuredObjectsViewPlace(usage.getCode());
        return new Hyperlink(structuredObjectsViewsMessages.structuredObjectsViewItem2(usage.getItemTitle(), usage
                .getTitle()), StringUtilities.getHrefByToken(historyMapper.getToken(place)));
    }
}
