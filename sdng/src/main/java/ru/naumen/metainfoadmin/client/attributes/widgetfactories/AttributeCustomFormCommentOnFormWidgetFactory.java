package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.customform.Constants;

/**
 * Фабрика представления аттрибута "Комментарий на форме" пользовательской формы
 * <AUTHOR>
 * @since 3 окт. 2017 г.
 */
public class AttributeCustomFormCommentOnFormWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Inject
    DispatchAsync dispatch;
    @Inject
    MetainfoServiceAsync metainfoService;
    @Inject
    private CustomFormMessages messages;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

        widget.addItem(new SimpleDtObject(Constants.CustomUserForm.NOT_FILL, messages.notFill(), ClassFqn.parse("")));
        widget.addItem(new SimpleDtObject(Constants.CustomUserForm.FILL, messages.fill(), ClassFqn.parse("")));
        widget.addItem(new SimpleDtObject(Constants.CustomUserForm.MUST_FILL, messages.mustFill(), ClassFqn.parse("")));

        callback.onSuccess(widget);
    }
}