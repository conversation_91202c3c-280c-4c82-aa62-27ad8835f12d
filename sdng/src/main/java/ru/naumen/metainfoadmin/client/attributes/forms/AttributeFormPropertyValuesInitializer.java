package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Класс, выполняющий инициализацию переменной propertyValues исходными значениями в зависимости от типа формы T 
 * <AUTHOR>
 * @since 01.06.2012
 */
public interface AttributeFormPropertyValuesInitializer<F extends ObjectForm>
{
    void init(IProperties contextProps, IProperties propertyValues, Attribute attribute, ReadyState rs);

    void loadRelatedData(IProperties contextProps, IProperties propertyValues, BasicCallback<Void> basicCallback);
}
