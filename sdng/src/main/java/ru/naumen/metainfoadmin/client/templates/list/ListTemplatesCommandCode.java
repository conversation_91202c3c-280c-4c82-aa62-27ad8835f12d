package ru.naumen.metainfoadmin.client.templates.list;

import java.util.Set;

import com.google.common.collect.ImmutableSet;

/**
 * Команды для шаблонов списков
 * <AUTHOR>
 * @since 06.04.2018
 */
public interface ListTemplatesCommandCode
{
    String EDIT = "editListTemplate";
    String DELETE = "deleteListTemplate";
    String EDIT_PARAMETERS = "editParametersListTemplate";

    Set<String> COMMANDS_IN_LIST = ImmutableSet.of(EDIT, DELETE);
}