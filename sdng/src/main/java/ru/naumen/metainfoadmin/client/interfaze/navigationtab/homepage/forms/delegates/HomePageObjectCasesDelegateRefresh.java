package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления свойства "Тип элемента" элемента домашней страницы
 * {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#OBJECT_CASES}
 * <AUTHOR>
 * @since 15.01.2023
 */
public class HomePageObjectCasesDelegateRefresh extends HomePageReferencePropertyRefreshDelegateBase<Collection<SelectItem>,
        MultiSelectBoxProperty>
{
    @Inject
    private ReferenceHelper referenceHelper;

    @Override
    public void refreshProperty(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (!isReference(context))
        {
            callback.onSuccess(false);
            return;
        }
        String referenceCardType = context.getPropertyValues().getProperty(HomePage.REFERENCE_CARD_TYPE, null);
        RelationsAttrTreeObject attrTree = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
        property.clearValue();
        property.getValueWidget().clear();
        property.getValueWidget().refreshPopupCellList();
        ClassFqn fqn = ReferenceHelper.getObjectClassFqnByCardType(new SimpleDtObject(referenceCardType, ""), attrTree);
        Predicate<? extends MetaClassLite> filter = attrTree == null
                ? Predicates.alwaysTrue()
                : ReferenceHelper.getAttributeChainPermittedClasses(attrTree.getAttribute());
        referenceHelper.fillObjectCasesProperty(fqn, context, property, filter, callback);
    }
}