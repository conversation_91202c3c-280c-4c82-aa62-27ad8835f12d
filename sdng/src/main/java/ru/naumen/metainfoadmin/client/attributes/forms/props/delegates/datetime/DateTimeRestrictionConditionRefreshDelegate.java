package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.core.client.attr.DateTimeRestrictionAttributeClientTool.getRestrictionType;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Условие" для атрибутов с ограничением типа "Задать зависимость от атрибута"
 * <AUTHOR>
 * @since 11 дек. 2018 г.
 */
public class DateTimeRestrictionConditionRefreshDelegate<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean isDateTimeAttr = Constants.DATE_TIME_TYPES.contains(attrType);
        RestrictionType restrictionType = isDateTimeAttr
                ? getRestrictionType(context)
                : null;
        boolean isRestrictionAttribute = RestrictionType.ATTRIBUTE_RESTRICTION == restrictionType;
        callback.onSuccess(isDateTimeAttr && isRestrictionAttribute);
    }

}
