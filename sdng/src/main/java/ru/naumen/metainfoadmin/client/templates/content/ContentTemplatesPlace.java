package ru.naumen.metainfoadmin.client.templates.content;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.activity.AbstractTreePlace;

/**
 * {@link com.google.gwt.place.shared.Place} для шаблонов контентов.
 * <AUTHOR>
 * @since Mer 15, 2021
 */
public class ContentTemplatesPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ContentTemplatesPlace>
    {
        @Override
        public ContentTemplatesPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(ContentTemplatesPlace place)
        {
            return StringUtilities.EMPTY;
        }
    }

    public static final String PLACE_PREFIX = "contentTemplates";

    public static final ContentTemplatesPlace INSTANCE = new ContentTemplatesPlace();

    public ContentTemplatesPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "ContentTemplatesPlace";
    }
}
