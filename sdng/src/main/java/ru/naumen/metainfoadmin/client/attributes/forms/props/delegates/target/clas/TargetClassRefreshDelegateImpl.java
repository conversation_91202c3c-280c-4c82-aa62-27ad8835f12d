package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.CommonUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Обработчик обновления свойства "Объект" (для атрибутов типа "Ссылка на БО" или "Набор ссылок на БО")
 * <AUTHOR>
 * @since 17.05.2012
 */
public abstract class TargetClassRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    protected class RefreshCallback extends BasicCallback<List<MetaClassLite>>
    {
        protected final AsyncCallback<Boolean> callback;
        protected final PropertyContainerContext context;
        protected final ListBoxProperty property;

        public RefreshCallback(AsyncCallback<Boolean> callback, PropertyContainerContext context,
                ListBoxProperty property)
        {
            this.callback = callback;
            this.context = context;
            this.property = property;
        }

        protected Collection<MetaClassLite> getPossibleClasses(List<MetaClassLite> classesList)
        {
            return metainfoUtils.getPossible(classesList, true);
        }

        @Override
        protected void handleSuccess(List<MetaClassLite> classesList)
        {
            classesList.sort(ru.naumen.core.shared.utils.CommonUtils.METACLASSLITE_COMPARATOR);
            SingleSelectCellList<String> selectList = property.getValueWidget();
            selectList.clear();
            for (MetaClassLite clz : getPossibleClasses(classesList))
            {
                if (ru.naumen.core.shared.Constants.Root.FQN.isSameClass(clz.getFqn()))
                {
                    continue;
                }
                selectList.addItem(clz.getTitle(), clz.getFqn().toString());
            }
            if (!getPossibleClasses(classesList).isEmpty())
            {
                selectList.setValue(selectList.getItem(0));
            }
        }

        protected void setRelatedMetainfo(String value, AsyncCallback<Boolean> callback)
        {
            if (StringUtilities.isEmpty(value))
            {
                context.getContextValues().setProperty(AttributeFormContextValues.RELATED_METAINFO, null);
                callback.onSuccess(true);
                return;
            }
            metainfoService.getMetaClass(ClassFqn.parse(value), new CallbackDecorator<MetaClass, Boolean>(callback)
            {
                @Override
                protected Boolean apply(MetaClass metaClass)
                {
                    context.getContextValues().setProperty(AttributeFormContextValues.RELATED_METAINFO, metaClass);
                    return true;
                }
            });
        }
    }

    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    CommonUtils commonUtils;
}
