package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import static ru.naumen.metainfo.shared.Constants.HomePage.REFERENCE_TAB_VALUE;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.*;

import java.util.ArrayList;
import java.util.HashSet;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfo.shared.Constants.HomePage;

/**
 * Делегат изменения значения свойства "Ссылка на карточку" для виджета {@link HomePage#REFERENCE_CARD_TYPE}
 *
 * <AUTHOR>
 * @since 17.01.2023
 */
public class HomePageTypeOfCardVCHDelegate implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.setProperty(REFERENCE_TAB_VALUE, null);
        context.setProperty(ATTRIBUTE_CHAIN, null);
        context.setProperty(REFERENCE_VALUE, null);
        context.setProperty(OBJECT_CLASS, null);
        context.setProperty(OBJECT_CASES, new HashSet<>());

        context.setProperty(TAB_UUIDS, new ArrayList<>());
        context.getPropertyControllers().get(OBJECT_CLASS).refresh();
        context.getPropertyControllers().get(OBJECT_CASES).refresh();
        context.getPropertyControllers().get(REFERENCE_VALUE).refresh();
        context.getPropertyControllers().get(HomePage.REFERENCE_TAB_VALUE).refresh();
        context.getPropertyControllers().get(ATTRIBUTE_CHAIN).refresh();
    }
}