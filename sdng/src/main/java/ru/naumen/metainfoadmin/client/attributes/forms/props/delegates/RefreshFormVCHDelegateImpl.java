package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormContextPropertiesSetter;

/**
 * Делегат изменения значения свойства формы атрибута, который вызывает перерисовку всей формы
 * <AUTHOR>
 * @since 05.07.2012
 *
 */
public class RefreshFormVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Inject
    AttributeFormContextPropertiesSetter<F> contextPropsSetter;

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        contextPropsSetter.setContextProperties(context);
        context.getRefreshProcess().startForContainer();
        String nextOperation = context.getRefreshProcess().getNextOperation();
        PropertyController propertyController = context.getPropertyControllers().get(nextOperation);
        propertyController.refresh();
    }
}
