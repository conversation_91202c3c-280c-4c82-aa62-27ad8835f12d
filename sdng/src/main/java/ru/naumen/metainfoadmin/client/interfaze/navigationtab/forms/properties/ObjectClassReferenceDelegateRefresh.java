package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления для виджета {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#OBJECT_CLASS}
 *
 * <AUTHOR>
 * @since 12.03.2022
 */
public class ObjectClassReferenceDelegateRefresh extends ReferencePropertyDelegateRefreshBase<String, TextBoxProperty>
{
    @Inject
    private ReferenceHelper referenceHelper;

    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (!isReference(context))
        {
            callback.onSuccess(false);
            return;
        }
        DtObject typeOfCard = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
        RelationsAttrTreeObject attrTree = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
        referenceHelper.fillObjectClassValue(typeOfCard, attrTree, property, context, callback);
    }
}