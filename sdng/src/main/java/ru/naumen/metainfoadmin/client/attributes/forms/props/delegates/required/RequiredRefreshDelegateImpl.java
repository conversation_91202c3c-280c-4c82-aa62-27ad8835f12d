package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.required;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DETERMINABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.INHERIT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.REQUIRED;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 */
public class RequiredRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(DETERMINABLE);
        String typeCode = context.getPropertyValues().getProperty(ATTR_TYPE);
        boolean related = AttributeOfRelatedObjectSettings.CODE.equals(typeCode);
        boolean inherited = Boolean.TRUE.equals(context.getPropertyValues().getProperty(INHERIT));
        boolean canChangeRequired = !Constants.TYPES_WITH_IMMUTABLE_REQUIRED.contains(typeCode);
        if (determinable)
        {
            context.setPropertyEnabled(REQUIRED, true);
        }
        context.setPropertyEnabled(REQUIRED, !related && !inherited && canChangeRequired);

        callback.onSuccess(!computable);
    }
}
