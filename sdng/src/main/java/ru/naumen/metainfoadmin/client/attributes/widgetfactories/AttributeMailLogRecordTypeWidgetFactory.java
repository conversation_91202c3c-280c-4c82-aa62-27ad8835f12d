package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dispatch.GetAllMailLogRecordTypesAction;
import ru.naumen.core.shared.dispatch.GetAllMailLogRecordTypesResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;

/**
 *
 * <AUTHOR>
 *
 */
public class AttributeMailLogRecordTypeWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        dispatch.execute(new GetAllMailLogRecordTypesAction(), new BasicCallback<GetAllMailLogRecordTypesResponse>()
        {

            @Override
            public void handleSuccess(GetAllMailLogRecordTypesResponse response)
            {
                SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

                Collection<SimpleDtObject> types = response.getTypes();

                for (SimpleDtObject dto : types)
                {
                    widget.addItem(dto);
                }

                callback.onSuccess(widget);
            }
        });

    }

}
