package ru.naumen.metainfoadmin.client.attributes.forms.add;

import jakarta.inject.Inject;

import com.google.inject.Singleton;

import ru.naumen.core.client.validation.CodeValidator;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyControllerFactorySyncImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
@Singleton
public class AddAttributeFormPropertyControllerFactorySyncImpl
        extends AttributeFormPropertyControllerFactorySyncImpl<ObjectFormAdd>
{
    @Inject
    public void setUpValidators(CodeValidator codeValidator)
    {
        codeValidators.put(codeValidator, AttributeFormValidationCode.DEFAULT);
    }
}
