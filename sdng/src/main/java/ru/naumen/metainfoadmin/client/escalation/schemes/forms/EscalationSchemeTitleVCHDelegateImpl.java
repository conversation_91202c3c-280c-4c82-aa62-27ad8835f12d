package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import static ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.MAX_CODE_LENGTH;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 06.02.2017
 * Делегат при изменении названия
 */
public class EscalationSchemeTitleVCHDelegateImpl implements PropertyDelegateVCH
{
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private SecurityHelper security;

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String codeValue = context.getPropertyValues().getProperty(EscalationSchemeFormPropertyCode.CODE);
        if (StringUtilities.isEmpty(codeValue))
        {
            String titleValue = context.getPropertyValues().getProperty(EscalationSchemeFormPropertyCode.TITLE);
            String specialSymbols = security.hasVendorProfile()
                    ? ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS_FOR_VENDOR_FOR_ESCALATION_SCHEME
                    : ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS;
            codeValue = transliterationService.transliterateToCode(titleValue, MAX_CODE_LENGTH, specialSymbols);
            context.getPropertyValues().setProperty(EscalationSchemeFormPropertyCode.CODE, codeValue);
            context.getPropertyControllers().get(EscalationSchemeFormPropertyCode.CODE).refresh();
        }
    }
}