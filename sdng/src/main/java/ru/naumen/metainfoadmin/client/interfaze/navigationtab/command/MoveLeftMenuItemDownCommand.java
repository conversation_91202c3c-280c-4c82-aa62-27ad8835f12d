package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.dispatch.MoveLeftMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Команда смещения элемента левого меню вниз
 * <AUTHOR>
 * @since 19.06.2020
 */
public class MoveLeftMenuItemDownCommand extends MoveMenuItemDownCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "moveLeftMenuItemDown";

    @Inject
    public MoveLeftMenuItemDownCommand(@Assisted NavigationSettingsLMCommandParam param)
    {
        super(param);
    }

    @Override
    protected MoveNavigationMenuItemAction getAction()
    {
        return new MoveLeftMenuItemAction();
    }
}
