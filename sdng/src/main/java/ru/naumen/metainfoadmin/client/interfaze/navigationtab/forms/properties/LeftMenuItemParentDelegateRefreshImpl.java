package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.List;

import com.google.inject.Singleton;

import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Делегат обновления свойства "Вложен в раздел" элемента левого меню
 * <AUTHOR>
 * @since 25.06.2020
 */
@Singleton
public class LeftMenuItemParentDelegateRefreshImpl extends MenuItemParentDelegateRefreshImpl<LeftMenuItemSettingsDTO>
{
    protected List<LeftMenuItemSettingsDTO> getMenuItems(NavigationSettings settings)
    {
        return settings.getLeftMenu().getChildren();
    }
}