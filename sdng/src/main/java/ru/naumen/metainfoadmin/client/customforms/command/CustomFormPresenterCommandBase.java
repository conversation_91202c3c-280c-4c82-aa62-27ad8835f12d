package ru.naumen.metainfoadmin.client.customforms.command;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
public abstract class CustomFormPresenterCommandBase extends PresenterCommandImpl<CustomForm, CustomForm, CustomForm>
{
    protected final MetaClass metaClassUnderEdit;

    public CustomFormPresenterCommandBase(ContentCommandParam<CustomForm, CustomForm> param)
    {
        super(param);
        metaClassUnderEdit = param.getContext().getMetainfo();
    }

    @Override
    public void onExecute(CustomForm result, CallbackDecorator<CustomForm, CustomForm> callback)
    {
        callback.onSuccess(result);
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }
}
