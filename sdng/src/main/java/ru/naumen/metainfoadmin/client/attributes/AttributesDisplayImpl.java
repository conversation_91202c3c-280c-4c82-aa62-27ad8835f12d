package ru.naumen.metainfoadmin.client.attributes;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.MetainfoUtils;

/**
 * @see AttributesDisplay
 *
 * <AUTHOR>
 * @since 13.08.2010
 *
 */
public class AttributesDisplayImpl<T extends AttributeListCustomizer> extends TitledAttributeListImpl implements AttributesDisplay<T>
{
    @Inject
    public AttributesDisplayImpl(MetainfoUtils metainfoUtils, T attributeListCustomizer)
    {
        super(metainfoUtils, attributeListCustomizer);
    }

    @Override
    public void setEditable(boolean editable)
    {
        // TODO Реализовать недоступность редактирования атрибутов у архивного класса http://projects.naumen.ru:8090/ServiceDesk/Releases/4.0/Requirements/Req00034
    }
}
