package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isDateOrDateTimeAttribute;

import java.util.Objects;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат биндинга свойства "Атрибут" для атрибутов с ограничением типа "Задать зависимость от атрибута"
 * <AUTHOR>
 * @since 11 дек. 2018 г.
 */
public class AttributeDateTimeRestrictionBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<SelectItem, ListBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty>
{

    private final MetainfoServiceAsync metainfoService;

    @Inject
    public AttributeDateTimeRestrictionBindDelegateImpl(MetainfoServiceAsync metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        MetaClassLite metaClassLite = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attributeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        metainfoService.getMetaClass(metaClassLite.getFqn(), new BasicCallback<MetaClass>(context.getDisplay())
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                super.handleSuccess(metaClass);
                bindProperty(context, property, attributeCode, metaClass, callback);
            }
        });
    }

    private void bindProperty(PropertyContainerContext context, ListBoxProperty property, String attributeCode,
            MetaClass metaClass, AsyncCallback<Void> callback)
    {
        SingleSelectCellList<String> valueWidget = property.getValueWidget();
        valueWidget.clear();
        valueWidget.setHasEmptyOption(true);
        metaClass.getAttributes().stream()
                .filter(attr -> isDateOrDateTimeAttribute(attr) && !Objects.equals(attr.getCode(), attributeCode))
                .sorted(CommonUtils.ITITLED_COMPARATOR)
                .forEach(attr -> valueWidget.addItem(attr.getTitle(), attr.getCode()));
        super.bindProperty(context, property, callback);
    }

}
