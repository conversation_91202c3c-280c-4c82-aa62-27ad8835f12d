package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.ITitled;
import ru.naumen.metainfo.shared.elements.HasEnabled;

/**
 * Модель вкладки для редактирования шаблона панели вкладок.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class TabModel implements ITitled, HasCode, HasEnabled
{
    private String title = StringUtilities.EMPTY;
    private String originalTitle = StringUtilities.EMPTY;
    private boolean enabled = false;
    private String code = StringUtilities.EMPTY;

    public TabModel()
    {
    }

    @Override
    public String getCode()
    {
        return code;
    }

    public String getOriginalTitle()
    {
        return originalTitle;
    }

    @Override
    public String getTitle()
    {
        return title;
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public void setOriginalTitle(String originalTitle)
    {
        this.originalTitle = originalTitle;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }
}
