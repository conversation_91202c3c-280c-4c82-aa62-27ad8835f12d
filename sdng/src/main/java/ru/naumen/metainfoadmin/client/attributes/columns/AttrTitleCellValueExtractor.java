package ru.naumen.metainfoadmin.client.attributes.columns;

import ru.naumen.admin.client.widgets.AdminWidgetResources;

public class AttrTitleCellValueExtractor extends ByTagAndClassNameCellValueExtractor
{

    @Override
    String getTagName()
    {
        return TAG_NAME_SPAN;
    }

    @Override
    String getClassName()
    {
        return AdminWidgetResources.INSTANCE.attributeList().titleBadge();
    }
}
