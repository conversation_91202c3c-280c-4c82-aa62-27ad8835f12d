package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerAfterBindHandler;

/**
 * Действия после окончания биндинга всех свойств
 * <AUTHOR>
 * @since 31.05.2012
 */
public interface AttributeFormAfterBindHandler<F extends ObjectForm> extends PropertyContainerAfterBindHandler
{
}
