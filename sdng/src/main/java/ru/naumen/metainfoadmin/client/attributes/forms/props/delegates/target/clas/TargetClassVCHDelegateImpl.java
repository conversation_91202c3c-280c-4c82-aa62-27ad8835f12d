package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas;

import static ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormContextValues.PERMITTED_TYPES_FQNS;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.RELATED_METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class TargetClassVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    private class TargetClassMetaClassCallback extends BasicCallback<MetaClass>
    {
        PropertyContainerContext context;

        TargetClassMetaClassCallback(PropertyContainerContext context)
        {
            this.context = context;
        }

        @Override
        protected void handleSuccess(MetaClass metaClass)
        {
            context.getContextValues().setProperty(PERMITTED_TYPES_FQNS, null);
            context.getContextValues().setProperty(RELATED_METAINFO, metaClass);

            context.getRefreshProcess()
                    .startCustomProcess(Lists.newArrayList(EDIT_PRS, DEFAULT_VALUE, COMPLEX_RELATION_ATTR_GROUP,
                            QUICK_ADD_FORM_CODE, QUICK_EDIT_FORM_CODE));
            context.getPropertyControllers().get(PERMITTED_TYPES).refresh();
        }
    }

    @Inject
    private AdminMetainfoServiceAsync metainfo;

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        TargetClassMetaClassCallback callback = new TargetClassMetaClassCallback(context);

        String strValue = context.getPropertyValues().getProperty(TARGET_CLASS);
        if (StringUtilities.isEmpty(strValue))
        {
            callback.onSuccess(null);
            return;
        }
        metainfo.getMetaClass(ClassFqn.parse(strValue), callback);
    }
}
