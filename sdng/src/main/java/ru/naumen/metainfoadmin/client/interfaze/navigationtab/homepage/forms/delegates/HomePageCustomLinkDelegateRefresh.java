package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import jakarta.inject.Inject;

import com.google.gwt.http.client.UrlBuilder;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.validation.CustomLinkValidator;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.homepage.HomePageType;

/**
 * Логика обновления свойства "Адрес ссылки" для "Произвольной ссылки" {@link HomePage#CUSTOM_LINK_VALUE}
 * <AUTHOR>
 * @since 20.01.2023
 */
public class HomePageCustomLinkDelegateRefresh implements PropertyDelegateRefresh<String, TextBoxProperty>
{
    private final CommonMessages cmessages;
    private final CustomLinkValidator validator;

    @Inject
    public HomePageCustomLinkDelegateRefresh(CommonMessages cmessages, CustomLinkValidator validator)
    {
        this.cmessages = cmessages;
        this.validator = validator;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(HomePage.TYPE);
        HomePageType type = HomePageType.valueOf(typeStr);

        boolean isCustomLink = HomePageType.CUSTOM_LINK.equals(type);
        UrlBuilder builder = new UrlBuilder().setProtocol(Location.getProtocol()).setHost(Location.getHost());
        property.setDescription(cmessages.afterSystemUrl() + " " + builder.buildString());
        validator.setStartsWithSystemUrl(true);
        callback.onSuccess(isCustomLink);
        if (!isCustomLink)
        {
            context.getPropertyControllers().get(HomePage.CUSTOM_LINK_VALUE).unbindValidators();
        }
    }
}