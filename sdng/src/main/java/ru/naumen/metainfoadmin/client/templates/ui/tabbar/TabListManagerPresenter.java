package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import static ru.naumen.commons.shared.utils.StringUtilities.EMPTY;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.TableRowElement;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CellTableWithRowsId;
import ru.naumen.core.client.listeditor.ListEditorResources;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.DataTableDnDGroupController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.columns.HasEnabledColumn;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.TabModelCommandCodes;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.TabModelCommandParam;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Представление редактора списка вкладок для шаблона панели вкладок.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class TabListManagerPresenter extends BasicPresenter<TabListManagerDisplay>
{
    private class TabListEditorDnDControllerImpl extends DataTableDnDGroupController
    {
        public TabListEditorDnDControllerImpl()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(int oldPosition, int newPosition, ReadyState readyState)
        {
            moveItem(oldPosition + 1, newPosition - oldPosition);
        }

        @Override
        protected List<Element> retrieveChildren()
        {
            List<Element> elements = new ArrayList<>();
            NodeList<Element> trs = getRootElement().getElementsByTagName(TableRowElement.TAG);
            for (int i = 1; i < trs.getLength() - 1; ++i)
            {
                elements.add(trs.getItem(i));
            }
            return elements;
        }
    }

    @Inject
    private ObjectListColumnBuilder columnBuilder;
    @Inject
    private Provider<TabTitleColumn> titleColumnProvider;
    @Inject
    private Provider<HasEnabledColumn<TabModel>> enabledColumnProvider;
    @Inject
    private ListEditorResources resources;

    private final AsyncCallback<Void> refreshCallback = new BasicCallback<Void>()
    {
        @Override
        protected void handleSuccess(Void value)
        {
            context.getEventBus().fireEvent(new ChangeUITemplateEvent(true));
            refreshDisplay();
        }
    };

    private UIContext context;
    private final List<TabModel> tabs = new ArrayList<>();

    @Inject
    public TabListManagerPresenter(TabListManagerDisplay display, EventBus eventBus,
            ListEditorDnDControllerFactory dndControllerFactory)
    {
        super(display, eventBus);
        ListEditorDnDController dndController = dndControllerFactory.create(new TabListEditorDnDControllerImpl());
        ((CellTableWithRowsId<?, ?>)display.getTable()).setDnDController(dndController);
    }

    public void init(UIContext context)
    {
        this.context = context;
    }

    public List<TabModel> getTabs()
    {
        return tabs;
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().getTable().setRowData(0, tabs);
        getDisplay().getTable().setRowCount(tabs.size());
    }

    @Override
    protected void onBind()
    {
        bindTable();
        refreshDisplay();
    }

    private void addActionColumn(String command)
    {
        TabModelCommandParam commandParam = new TabModelCommandParam(null, refreshCallback, context, tabs);
        columnBuilder.addActionColumn(display, commandParam, command);
    }

    private void bindColumns()
    {
        getDisplay().getTable().addColumn(titleColumnProvider.get());
        getDisplay().getTable().addColumn(enabledColumnProvider.get());
    }

    private void bindTable()
    {
        addActionColumn(TabModelCommandCodes.MOVE_TAB_UP);
        addActionColumn(TabModelCommandCodes.MOVE_TAB_DOWN);
        bindColumns();
        addActionColumn(TabModelCommandCodes.EDIT_TAB);
        addActionColumn(TabModelCommandCodes.TOGGLE_TAB);

        getDisplay().getTable().setRowStyles((tabModel, index) ->
                StringUtilities.isEmpty(tabModel.getCode()) ? resources.cellTableStyle().headRow() : EMPTY);
    }

    private void moveItem(final int index, final int direction)
    {
        tabs.add(index + direction, tabs.remove(index));
        refreshCallback.onSuccess(null);
    }
}
