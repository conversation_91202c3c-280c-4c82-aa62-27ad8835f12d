package ru.naumen.metainfoadmin.client.templates.list.commands;

import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.FxExceptions;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.templates.list.dispatch.CheckNavigationMenuSettingsListTemplateAction;
import ru.naumen.metainfo.shared.templates.list.dispatch.DeleteListTemplatesAction;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;

/**
 * Команда удаления шаблона списка
 * <AUTHOR>
 * @since 19.04.2018
 */
public class DelListTemplateCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    protected class DeleteListTemplateWithCheck extends ExecuteCallback
    {
        public DeleteListTemplateWithCheck(CommandParam<Collection<DtObject>, Void> param)
        {
            super(param);
        }

        @Override
        protected void onYes(Dialog dialog)
        {
            dispatch.execute(new CheckNavigationMenuSettingsListTemplateAction(param.getValue()
                            .stream()
                            .map(IUUIDIdentifiable::getUUID)
                            .collect(Collectors.toList())),
                    new BasicCallback<SimpleResult<String>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<String> result)
                        {
                            String actionMessage = result.get();

                            if (StringUtilities.isNotEmpty(actionMessage))
                            {
                                dialogs.error(actionMessage);
                                dialog.hide();
                            }
                            else
                            {
                                DeleteListTemplateWithCheck.super.onYes(dialog);
                            }
                        }
                    });
        }
    }

    @Inject
    private CommonMessages cmessages;
    @Inject
    private ListTemplatesMessages listTemplatesMessages;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public DelListTemplateCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        if (values.size() == 1)
        {
            return listTemplatesMessages.listTemplateConfirmDelete(values.iterator().next().getTitle());
        }
        return listTemplatesMessages.listTemplateConfirmMassDelete();
    }

    @Override
    public void execute(CommandParam<Collection<DtObject>, Void> param)
    {
        param = prepareParam(param);
        question(param, new DeleteListTemplateWithCheck(param));
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        dispatch.execute(
                new DeleteListTemplatesAction(
                        param.getValue()
                                .stream()
                                .map(IUUIDIdentifiable::getUUID)
                                .collect(Collectors.toList())),
                new BasicCallback<EmptyResult>()
                {
                    @Override
                    protected void handleFailure(String msg, @Nullable String details)
                    {
                        param.getCallback().onFailure(new FxExceptions(msg));
                    }

                    @Override
                    protected void handleSuccess(EmptyResult result)
                    {
                        param.getCallback().onSuccess(null);
                    }
                });
    }
}