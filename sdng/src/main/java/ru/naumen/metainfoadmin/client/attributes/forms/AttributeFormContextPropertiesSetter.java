package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

/**
 * Класс вычисляет различные вспомогательные значения по значениям свойств и записывает их в context properties
 * <AUTHOR>
 * @since 31.05.2012
 */
public interface AttributeFormContextPropertiesSetter<F extends ObjectForm>
{
    void setContextProperties(PropertyContainerContext context);
}
