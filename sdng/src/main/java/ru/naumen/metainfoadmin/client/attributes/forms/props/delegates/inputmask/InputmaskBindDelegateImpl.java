package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.InputMaskDefinitionsFormatValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 *
 */
public class InputmaskBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<String, FootedTextBoxProperty> implements InputMaskModeChangeHandler
{
    @Inject
    private AdminDialogMessages messages;
    @Inject
    private Dialogs dialog;
    @Inject
    private AttributesMessages attrMessages;
    @Inject
    private InputMaskDefinitionsFormatValidator maskDefinitionsFormatValidator;

    private ValidationUnit<String> validationUnit;
    private PropertyContainerContext context;
    private FootedTextBoxProperty property;

    @Override
    public void bindProperty(final PropertyContainerContext context, FootedTextBoxProperty property,
            AsyncCallback<Void> callback)
    {
        this.context = context;
        this.property = property;
        context.getEventBus().addHandler(InputMaskModeChangeEvent.getType(), this);
        String inputMaskMode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE);
        bindMaskDefinitionsFormatValidator(inputMaskMode);
        property.getValueWidget().setFootedText(messages.help());
        property.getValueWidget().addClickHandler(event -> dialog.info(messages.help(), getHelpText()));
        callback.onSuccess(null);
    }

    @Override
    public void onInputMaskModeChange(InputMaskModeChangeEvent event)
    {
        PropertyController controller = context.getPropertyControllers().get(AttributeFormPropertyCode.INPUTMASK);
        if (!(controller instanceof PropertyControllerImpl))
        {
            return;
        }
        String inputMaskMode = event.getInputMaskMode();
        if (StringAttributeType.INPUT_MASK_DEFINITIONS.equals(inputMaskMode) && validationUnit == null)
        {

            bindMaskDefinitionsFormatValidator(inputMaskMode);
        }
        else if (validationUnit != null)
        {
            unbindMaskDefinitionsFormatValidator();
        }
    }

    private void bindMaskDefinitionsFormatValidator(String inputMaskMode)
    {
        if (StringAttributeType.INPUT_MASK_DEFINITIONS.equals(inputMaskMode))
        {
            Processor validationProcessor = context.getValidation().get(AttributeFormValidationCode.DEFAULT);
            validationUnit = validationProcessor.validate(property, maskDefinitionsFormatValidator);
        }
    }

    private void unbindMaskDefinitionsFormatValidator()
    {
        ((HasValidation)validationUnit.getValue()).initValidation();
        validationUnit.unregister();
        validationUnit = null;
    }

    private String getHelpText()
    {
        String inputMaskMode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE);
        if (StringAttributeType.INPUT_MASK_ALIAS.equals(inputMaskMode))
        {
            return attrMessages.inputmaskAliasHelp();
        }
        else if (StringAttributeType.INPUT_MASK_DEFINITIONS.equals(inputMaskMode))
        {
            return attrMessages.inputmaskDefinitionsHelp();
        }
        else if (StringAttributeType.INPUT_MASK_REGEX.equals(inputMaskMode))
        {
            return attrMessages.inputmaskRegexHelp();
        }
        return "";
    }
}