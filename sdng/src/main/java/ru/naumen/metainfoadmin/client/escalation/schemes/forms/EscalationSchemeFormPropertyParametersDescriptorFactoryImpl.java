package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import com.google.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EscalationSchemeFormPropertyParametersDescriptorFactoryImpl<F extends ObjectForm>
        extends PropertyParametersDescriptorFactoryImpl<EscalationScheme, F>
{
    @Inject
    AttributesMessages attrMessages;
    @Inject
    AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        //@formatter:off
        registerOrModifyProperty(EscalationSchemeFormPropertyCode.TITLE,            cmessages.title(),                 true,  "title",            0, true, true);
        registerOrModifyProperty(EscalationSchemeFormPropertyCode.CODE,             cmessages.code(),                  true,  "code",             1, true, true);
        registerOrModifyProperty(EscalationSchemeFormPropertyCode.DESCRIPTION,      cmessages.description(),           false, "description",      2, true, true);
        registerOrModifyProperty(EscalationSchemeFormPropertyCode.TARGET_OBJECTS,   cmessages.objects(),               true,  "targetObjects",    3, true, true);
        registerOrModifyProperty(EscalationSchemeFormPropertyCode.TIMER,            attrMessages.timerDefinition(),    true,  "timer",            4, true, true);
        registerOrModifyProperty(EscalationSchemeFormPropertyCode.SETTINGS_SET,     adminDialogMessages.settingsSet(), false, "settingsSet",      5, true, false);
        //@formatter:on
    }
}
