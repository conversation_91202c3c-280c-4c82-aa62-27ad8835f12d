package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inherit;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат, осуществляющий биндинг свойства "Наследовать параметры" - название определяется в зависимости от того,
 * находится атрибут в типе или в классе
 * <AUTHOR>
 * @since 17.05.2012
 */
public class InheritBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<Boolean, BooleanCheckBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    AttributesMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Void> callback)
    {
        Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        property.setCaption(getTitle(attr));
        property.ensureDebugId("inherit");
        super.bindProperty(context, property, callback);
    }

    private String getTitle(Attribute attr)
    {
        if (attr.getMetaClassLite().getFqn().isClass())
        {
            return messages.useSystemParams();
        }
        else
        {
            return messages.inheritParams();
        }
    }
}
