package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;

/**
 * Делегат отрисовки свойства "Вид элемента" {@link HomePage#TYPE}
 * <AUTHOR>
 * @since 17.01.2023
 */
public class HomePageTypeBindDelegate extends PropertyDelegateBindImpl<SelectItem, ListBoxProperty>
{
    private final Map<HomePageType, String> homePageTypeTitles;

    @Inject
    public HomePageTypeBindDelegate(
            @Named(NavigationTabSettingsGinModule.HOME_PAGE_TYPE_TITLES)
            Map<HomePageType, String> homePageTypeTitles)
    {
        this.homePageTypeTitles = homePageTypeTitles;
    }

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        SingleSelectCellList<?> selList = property.getValueWidget();
        for (HomePageType type : HomePageType.values())
        {
            selList.addItem(homePageTypeTitles.get(type), type.name());
        }
        super.bindProperty(context, property, callback);
    }
}