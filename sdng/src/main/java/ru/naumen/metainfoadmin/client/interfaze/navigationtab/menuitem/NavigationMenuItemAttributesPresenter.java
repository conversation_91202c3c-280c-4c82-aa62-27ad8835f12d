package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemDisabledBy;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsMenuItemAbstractCommandParam;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * <AUTHOR>
 * @since 04 дек. 2013 г.
 *
 */
public abstract class NavigationMenuItemAttributesPresenter<M extends IMenuItem,
        C extends NavigationMenuItemContext<M>, P extends NavigationSettingsMenuItemAbstractCommandParam<M>> extends BasicPresenter<InfoDisplay>
{
    private final ArrayList<AttributePropertyDescription<?, C>> properties;
    private final NavigationSettingsMessages messages;
    private final ButtonFactory buttonFactory;
    private final CommonMessages cmessages;
    private final PlaceController placeController;
    private final TagsMessages tagsMessages;

    private ToolBarDisplayMediator<IMenuItem> toolBar;
    protected C context;
    private ButtonPresenter<IMenuItem> enableButtonPresenter;
    private ButtonPresenter<IMenuItem> disableButtonPresenter;
    private P refreshParam;
    private P deleteParam;

    private final AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new SafeBasicCallback<DtoContainer<NavigationSettings>>()
            {
                @Override
                protected void handleSuccess(DtoContainer<NavigationSettings> settings)
                {
                    refreshParam.setSettings(settings);
                    deleteParam.setSettings(settings);
                    context.setSettings(settings);
                    M item = getMenuItem(settings);
                    refreshParam.setValue(item);
                    deleteParam.setValue(item);
                    context.setItem(item);
                    refreshDisplay();
                }
            };

    private final AsyncCallback<DtoContainer<NavigationSettings>> deleteCallback =
            new BasicCallback<DtoContainer<NavigationSettings>>()
            {
                @Override
                protected void handleSuccess(@Nullable DtoContainer<NavigationSettings> settings)
                {
                    if (settings != null)
                    {
                        unbind();
                        placeController.goTo(new InterfaceSettingsPlace("navigation"));
                    }
                }
            };

    public NavigationMenuItemAttributesPresenter(InfoDisplay display, EventBus eventBus,
            ArrayList<AttributePropertyDescription<?, C>> properties,
            NavigationSettingsMessages messages,
            ButtonFactory buttonFactory, CommonMessages cmessages,
            PlaceController placeController, TagsMessages tagsMessages)
    {
        super(display, eventBus);
        this.properties = properties;
        this.messages = messages;
        this.buttonFactory = buttonFactory;
        this.cmessages = cmessages;
        this.placeController = placeController;
        this.tagsMessages = tagsMessages;
    }

    public void init(C context)
    {
        this.context = context;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        for (AttributePropertyDescription<?, C> propDesc : properties)
        {
            if (propDesc.isVisible(context))
            {
                propDesc.setValue(context);
            }
        }
        M item = context.getItem();
        boolean disabled = item instanceof LeftMenuItemSettingsDTO && Boolean.FALSE.equals(
                ((LeftMenuItemSettingsDTO)item).getProperty(Tag.IS_ELEMENT_ENABLED));
        if (disabled)
        {
            enableButtonPresenter.getDisplay().asWidget().setVisible(false);
            disableButtonPresenter.getDisplay().asWidget().setVisible(false);
        }
        else
        {
            if (item.isEnabled())
            {
                enableButtonPresenter.getDisplay().asWidget().setVisible(false);
                disableButtonPresenter.getDisplay().asWidget().setVisible(true);
            }
            else
            {
                enableButtonPresenter.getDisplay().asWidget().setVisible(true);
                disableButtonPresenter.getDisplay().asWidget().setVisible(false);
            }
        }
        String attentionMessage = StringUtilities.EMPTY;
        if (disabled)
        {
            String disabledByElement = ((LeftMenuItemSettingsDTO)item).getProperty(Tag.ELEMENT_DISABLED_BY);
            if (null != disabledByElement)
            {
                List<DtObject> tagsDtObjects =
                        Objects.requireNonNull(
                                ((LeftMenuItemSettingsDTO)item).getProperty(Tag.ELEMENT_DISABLED_BY_TAGS));
                String tags = tagsDtObjects.stream().map(DtObject::getTitle).collect(Collectors.joining(", "));
                switch (MenuItemDisabledBy.valueOf(disabledByElement))
                {
                    case parent:
                        attentionMessage = SafeHtmlUtils.htmlEscape(tagsMessages.disabledItemMenuByParentWarning(tags));
                        break;
                    case clazz:
                        attentionMessage = SafeHtmlUtils.htmlEscape(tagsMessages.disabledItemMenuByClassWarning(tags));
                        break;
                    case content:
                        attentionMessage = SafeHtmlUtils.htmlEscape(
                                tagsMessages.disabledItemMenuByContentWarning(tags));
                        break;
                    case attribute:
                        attentionMessage = SafeHtmlUtils.htmlEscape(
                                tagsMessages.disabledItemMenuByAttributeWarning(tags));
                        break;
                }
            }
            else
            {
                attentionMessage = SafeHtmlUtils.htmlEscape(tagsMessages.disabledItemMenuWarning());
            }
        }
        getDisplay().getAttention().setVisible(disabled);
        getDisplay().getAttention().setHTML(attentionMessage); // NOPMD NSDPRD-28509 unsafe html
        toolBar.refresh(context.getItem());
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(messages.menuElementAttributes());
        toolBar = new ToolBarDisplayMediator(getDisplay().getToolBar());
        refreshParam = getParam(refreshCallback);
        deleteParam = getParam(deleteCallback);
        enableButtonPresenter = (ButtonPresenter<IMenuItem>)buttonFactory.create(ButtonCode.SWITCH,
                cmessages.switchOn(), getEnableCommandId(),
                refreshParam);
        enableButtonPresenter.addPossibleFilter(this::hasEditPermission);

        disableButtonPresenter = (ButtonPresenter<IMenuItem>)buttonFactory.create(ButtonCode.SWITCH,
                cmessages.switchOff(),
                getDisableCommandId(), refreshParam);
        disableButtonPresenter.addPossibleFilter(this::hasEditPermission);
        toolBar.add(enableButtonPresenter);
        toolBar.add(disableButtonPresenter);
        ButtonPresenter<IMenuItem> editBtn = (ButtonPresenter<IMenuItem>)buttonFactory.create(ButtonCode.EDIT,
                cmessages.edit(), getEditCommandId(), refreshParam);
        editBtn.addPossibleFilter(this::hasEditPermission);
        toolBar.add(editBtn);

        ButtonPresenter<IMenuItem> delBtn = (ButtonPresenter<IMenuItem>)buttonFactory.create(ButtonCode.DELETE,
                cmessages.delete(), getDeleteCommandId(), deleteParam);
        delBtn.addPossibleFilter(this::hasDeletePermission);
        toolBar.add(delBtn);

        toolBar.bind();
        for (AttributePropertyDescription<?, C> propDesc : properties)
        {
            if (propDesc.isVisible(context))
            {
                getDisplay().add(propDesc.getProperty(), propDesc.getDebugId());
            }
        }
    }

    private boolean hasEditPermission(IMenuItem menuItem)
    {
        return AdminPermissionHolderUtils.hasPermission(context.getSettings(), PermissionType.EDIT, menuItem);
    }

    private boolean hasDeletePermission(IMenuItem menuItem)
    {
        return AdminPermissionHolderUtils.hasPermission(context.getSettings(), PermissionType.DELETE, menuItem);
    }

    protected abstract String getDeleteCommandId();

    protected abstract String getEditCommandId();

    protected abstract String getDisableCommandId();

    protected abstract String getEnableCommandId();

    protected abstract P getParam(AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback);

    protected abstract M getMenuItem(DtoContainer<NavigationSettings> settings);
}