package ru.naumen.metainfoadmin.client.customforms.form;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.metainfo.shared.Constants;

/**
 * Преобразователь названия формы в ее код.
 * <AUTHOR>
 * @since Feb 11, 2019
 */
@Singleton
public class CustomFormCodeCreator
{
    @Inject
    private SecurityHelper securityHelper;
    @Inject
    private TransliterationService transliterator;

    public String createCode(String title)
    {
        String specialChars = securityHelper.hasVendorProfile() ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                : Constants.CODE_SPECIAL_CHARS;
        return transliterator.transliterateToCode(title, Constants.MAX_CODE_LENGTH, specialChars);
    }
}
