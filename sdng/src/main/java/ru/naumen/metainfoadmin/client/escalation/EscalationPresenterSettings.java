package ru.naumen.metainfoadmin.client.escalation;

import jakarta.inject.Singleton;

/**
 * Настройки отображения вкладок эскалации.
 * Используется для реализации admin lite ограничений.
 * <AUTHOR>
 * @since Mar 10, 2016
 */
@Singleton
public class EscalationPresenterSettings
{
    private boolean withScheme = true;
    private boolean withTables = true;

    public boolean isWithScheme()
    {
        return withScheme;
    }

    public boolean isWithTables()
    {
        return withTables;
    }

    public void setWithScheme(boolean withScheme)
    {
        this.withScheme = withScheme;
    }

    public void setWithTables(boolean withTables)
    {
        this.withTables = withTables;
    }
}
