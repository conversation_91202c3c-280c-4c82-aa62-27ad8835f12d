package ru.naumen.metainfoadmin.client.templates.ui;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Локализация элементов интерфейса шаблонов карточек/форм.
 * <AUTHOR>
 * @since Jul 29, 2021
 */
@DefaultLocale("ru")
public interface UITemplateMessages extends Messages
{
    String confirmDeleteSimple();

    String confirmDeleteWithUsages(String usages);

    String createNewTemplate();

    String doYouReallyWantToLeaveTemplateMode();

    String editExistingTemplate();

    String manageTabs();

    String newTemplate();

    String originalTabTitle();

    String saveTemplate();

    String saveTemplateDialog();

    String setAsDefault();

    String startTemplateMode();

    String stopTemplateMode();

    String tabTitle();
}
