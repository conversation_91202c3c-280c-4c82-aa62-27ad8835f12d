package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат обновления свойства "Класс объектов".
 * <AUTHOR>
 * @since 08.08.18
 */
public class RelatedObjectMetaclassRefreshDelegateAdd<F extends ObjectForm>
        implements
        AttributeFormPropertyDelegateRefresh<F, String, TextBoxProperty>
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context,
            TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        IProperties propertyValues = context.getPropertyValues();
        String typeCode = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (!typeCode.equals(AttributeOfRelatedObjectSettings.CODE))
        {
            callback.onSuccess(false);
            return;
        }

        context.setPropertyEnabled(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS, false);
        property.setDisable();

        RelationsAttrTreeObject selectedAttr = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_CHAIN);
        if (selectedAttr == null)
        {
            propertyValues.setProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS, null);
            property.setValue(null);
        }
        else
        {
            final ClassFqn relatedFqn = ((ObjectAttributeType)selectedAttr.getAttribute().getType().cast())
                    .getRelatedMetaClass();
            metainfoService.getMetaClass(relatedFqn.fqnOfClass(), new BasicCallback<MetaClass>()
            {
                @Override
                protected void handleSuccess(MetaClass relatedClass)
                {
                    property.setValue(relatedClass.getTitle());
                }
            });

            propertyValues.setProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS, relatedFqn.asString());
            context.getPropertyControllers().get(AttributeFormPropertyCode.RELATED_OBJECT_HIERARCHY_LEVEL).refresh();
        }

        callback.onSuccess(true);
    }
}
