package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link HasProperties.Property} для отображения информации о
 * параметрах, имеющих числовой тип на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 02.08.2019
 */
public class LongAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    private Long value;

    public void create(String code, Long value)
    {
        this.value = value;
        createInt(code);
    }

    @Override
    protected void createInt(String code)
    {
        if (ObjectUtils.isEmpty(value))
        {
            return;
        }
        createProperty(code, String.valueOf(value), getCaption(code));
    }

    @Nullable
    private String getCaption(String code)
    {
        return Constants.IntegerAttributeType.CODE.equals(attribute.getType().getCode()) && code.equals(
                AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION) ? messages.digitsCountRestrictions() : null;
    }
}
