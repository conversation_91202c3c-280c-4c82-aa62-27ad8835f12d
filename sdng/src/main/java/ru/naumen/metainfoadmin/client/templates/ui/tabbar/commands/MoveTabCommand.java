package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import java.util.List;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabModel;

/**
 * Базовая команда перемещения вкладки при изменении шаблона.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public abstract class MoveTabCommand extends BaseCommandImpl<TabModel, Void>
{
    private final int direction;
    private final List<TabModel> model;

    public MoveTabCommand(TabModelCommandParam param, int direction)
    {
        super(param);
        this.direction = direction;
        this.model = param.getTabs();
    }

    @Override
    public void execute(CommandParam<TabModel, Void> param)
    {
        TabModel tab = param.getValue();
        int current = model.indexOf(tab);
        if (current < 0)
        {
            return;
        }
        model.remove(current);
        model.add(current + direction, tab);
        param.getCallbackSafe().onSuccess(null);
    }

    @Override
    public boolean isPossible(Object input)
    {
        TabModel tab = input instanceof TabModel ? (TabModel)input : null;
        if (null == tab || tab.getCode().isEmpty() || !model.contains(tab))
        {
            return false;
        }
        int index = model.indexOf(tab) + direction;
        return model.size() > index && index > 0;
    }
}
