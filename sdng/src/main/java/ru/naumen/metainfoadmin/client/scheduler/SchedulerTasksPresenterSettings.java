package ru.naumen.metainfoadmin.client.scheduler;

import java.util.Set;

import jakarta.inject.Singleton;

/**
 * <AUTHOR>
 * @since Jan 10, 2014
 */
@Singleton
public class SchedulerTasksPresenterSettings
{
    private Set<String> types = null;

    public boolean containsType(String type)
    {
        return types == null || types.contains(type);
    }

    public void setTypes(Set<String> types)
    {
        this.types = types;
    }

    public Set<String> getTypes()
    {
        return types;
    }
}
