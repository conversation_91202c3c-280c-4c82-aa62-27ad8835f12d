package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.safehtml.shared.UriUtils;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;
import com.google.inject.name.Names;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.CachedMetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;
import ru.naumen.metainfoadmin.client.sets.formatters.SettingsSetPropertyFormatter;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;

/**
 * GIN-модуль карточки домашней страницы
 *
 * <AUTHOR>
 * @since 24.01.2023
 */
public class NavigationHomePageGinModule extends AbstractGinModule
{
    public static class NavigationHomePageAttributesPropertyProvider
            implements Provider<ArrayList<AttributePropertyDescription<?, HomePageDtObject>>>
    {
        @Inject
        @Named(NavigationTabSettingsGinModule.HOME_PAGE_TYPE_TITLES)
        private Map<HomePageType, String> homePageTypeTitles;
        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> title;
        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> type;
        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> profiles;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> content;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> tags;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> template;

        @Inject
        private TagsMessages tagsMessages;
        @Inject
        private CommonMessages commonMessages;
        @Inject
        private NavigationSettingsMessages navMessages;
        @Inject
        private CommonHtmlTemplates templates;
        @Inject
        private TagPropertyFormatter tagPropertyFormatter;
        @Inject
        private AdminDialogMessages adminDialogMessages;
        @Inject
        SettingsSetOnFormCreator settingsSetOnFormCreator;
        @Inject
        CachedMetainfoServiceAsync cachedMetainfoServiceAsync;
        @Inject
        SettingsSetPropertyFormatter settingsSetPropertyFormatter;

        @Override
        public ArrayList<AttributePropertyDescription<?, HomePageDtObject>> get()
        {
            ArrayList<AttributePropertyDescription<?, HomePageDtObject>> result = new ArrayList<>();
            result.add(new AttributePropertyDescription<String, HomePageDtObject>(
                    commonMessages.title(),
                    title,
                    HomePage.TITLE,
                    getTitleFunction()));
            result.add(new AttributePropertyDescription<String, HomePageDtObject>(
                    commonMessages.homePageView(),
                    type,
                    HomePage.TYPE,
                    getTypeFunction()));
            result.add(new AttributePropertyDescription<String, HomePageDtObject>(
                    commonMessages.content(),
                    content,
                    HomePage.CONTENT,
                    getContentFunction()));
            result.add(
                    new AttributePropertyDescription<String, HomePageDtObject>(
                            navMessages.profiles(),
                            profiles,
                            HomePage.PROFILES,
                            getProfilesFunction()));
            result.add(new AttributePropertyDescription<String, HomePageDtObject>(
                    navMessages.cardTemplate(),
                    template,
                    HomePage.REFERENCE_UI_TEMPLATE,
                    getTemplateFunction()));
            result.add(
                    new AttributePropertyDescription<String, HomePageDtObject>(
                            tagsMessages.tags(),
                            tags,
                            HomePage.TAGS, getTagsFunction()));
            if (settingsSetOnFormCreator.isDisplayedOnCards())
            {
                result.add(new AttributePropertyDescription<>(
                        adminDialogMessages.settingsSet(),
                        settingsSetOnFormCreator.createFieldOnCard(), "Info.settingsSet",
                        getSettingsSetFunction()));
            }
            return result;
        }

        private Function<HomePageDtObject, String> getSettingsSetFunction()
        {
            return input ->
            {
                if (input == null)
                {
                    return "";
                }
                DtObject settingsSet = cachedMetainfoServiceAsync.getSettingsSet(input.getSettingsSet());
                return settingsSet != null
                        ? settingsSetPropertyFormatter.format(settingsSet.getUUID(), settingsSet.getTitle()).asString()
                        : "";
            };
        }

        private Function<HomePageDtObject, String> getTagsFunction()
        {
            return input ->
            {
                if (null == input)
                {
                    return StringUtilities.EMPTY;
                }
                Collection<DtObject> elementTags = input
                        .getProperty(Constants.Tag.ELEMENT_TAGS, new ArrayList<DtObject>());
                if (CollectionUtils.isEmpty(elementTags))
                {
                    return StringUtilities.EMPTY;
                }
                return tagPropertyFormatter.formatToAnchors(elementTags).asString();
            };
        }

        private Function<HomePageDtObject, String> getTemplateFunction()
        {
            return input ->
            {
                if (!HomePageType.REFERENCE.name().equals(input.getType()))
                {
                    return navMessages.notDefined();
                }
                String title = input.getProperty(HomePage.REFERENCE_UI_TEMPLATE_TITLE);
                return title == null ? StringUtilities.EMPTY : title;
            };
        }

        private static Function<HomePageDtObject, String> getProfilesFunction()
        {
            return input -> input.getProfiles().stream()
                    .map(ITitled::getTitle)
                    .collect(Collectors.joining(", "));
        }

        private Function<HomePageDtObject, String> getContentFunction()
        {
            return input ->
            {
                if (input == null)
                {
                    return StringUtilities.EMPTY;
                }
                if (HomePageType.valueOf(input.getType()).equals(HomePageType.CUSTOM_LINK))
                {
                    return templates.anchor(input.getContentValue(),
                            UriUtils.fromString(input.getCustomLink()), true,
                            SafeHtmlUtils.EMPTY_SAFE_HTML).asString();
                }
                return input.getContentValue();
            };
        }

        private Function<HomePageDtObject, String> getTypeFunction()
        {
            return input -> input == null
                    ? StringUtilities.EMPTY
                    : homePageTypeTitles.get(HomePageType.valueOf(input.getType()));
        }

        private static Function<HomePageDtObject, String> getTitleFunction()
        {
            return input -> input == null ? StringUtilities.EMPTY : input.getTitle();
        }
    }

    public static final String NAVIGATION_HOME_PAGE_ATTRIBUTES = "navigationHomePageAttributes";

    @Override
    protected void configure()
    {
        bind(new TypeLiteral<ArrayList<AttributePropertyDescription<?, HomePageDtObject>>>()
        {
        })
                .annotatedWith(Names.named(NAVIGATION_HOME_PAGE_ATTRIBUTES))
                .toProvider(NavigationHomePageAttributesPropertyProvider.class);
    }
}