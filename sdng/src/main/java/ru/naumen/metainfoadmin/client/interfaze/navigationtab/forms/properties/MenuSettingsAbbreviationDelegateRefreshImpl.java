package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;
import ru.naumen.core.shared.utils.UserQuickAccessTilesHelper;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.QuickAccessTilePropertyCode;

/**
 * Делегат обновления значения свойства "Сокращение"
 *
 * <AUTHOR>
 * @since 25.06.2020
 */
@Singleton
public class MenuSettingsAbbreviationDelegateRefreshImpl
        implements PropertyDelegateRefresh<String, TextBoxProperty>
{
    @Inject
    private I18nUtil i18nUtil;

    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String presStr = context.getPropertyValues().getProperty(MenuSettingsPropertyCode.PRESENTATION);
        if (StringUtilities.isEmpty(presStr))
        {
            callback.onSuccess(true);
            return;
        }
        MenuIconType presentation = MenuIconType.valueOf(presStr);

        if (!MenuIconType.ABBREVIATION.equals(presentation))
        {
            callback.onSuccess(false);
            return;
        }

        String abbrev = context.getPropertyValues().getProperty(MenuSettingsPropertyCode.ABBREVIATION);
        if (StringUtilities.isNotEmpty(abbrev))
        {
            context.getPropertyValues().setProperty(MenuSettingsPropertyCode.ABBREVIATION, abbrev.toUpperCase());
            callback.onSuccess(true);
            return;
        }

        String titleStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TITLE);
        if (StringUtilities.isEmpty(titleStr))
        {
            LeftMenuItemSettingsDTO menuItem = context.getPropertyValues().getProperty(
                    QuickAccessTilePropertyCode.MENU_ITEM);
            if (menuItem != null)
            {
                titleStr = i18nUtil.getLocalizedTitle(menuItem);
            }
        }

        if (StringUtilities.isEmpty(titleStr))
        {
            callback.onSuccess(true);
            return;
        }

        String abbreviation = UserQuickAccessTilesHelper.generateAbbreviationByTitle(titleStr);
        context.setProperty(MenuSettingsPropertyCode.ABBREVIATION, abbreviation);

        callback.onSuccess(true);
    }
}