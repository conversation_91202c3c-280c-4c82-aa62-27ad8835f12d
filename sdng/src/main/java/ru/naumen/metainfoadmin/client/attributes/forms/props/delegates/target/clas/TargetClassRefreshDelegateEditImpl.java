package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas;

import java.util.Collection;
import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class TargetClassRefreshDelegateEditImpl extends TargetClassRefreshDelegateImpl<ObjectFormEdit>
{
    protected class EditRefreshCallback extends RefreshCallback
    {
        public EditRefreshCallback(AsyncCallback<Boolean> callback, PropertyContainerContext context,
                ListBoxProperty property)
        {
            super(callback, context, property);
        }

        @Override
        protected Collection<MetaClassLite> getPossibleClasses(List<MetaClassLite> classesList)
        {
            return classesList;
        }

        @Override
        protected void handleSuccess(List<MetaClassLite> classesList)
        {
            super.handleSuccess(classesList);
            Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
            String value;

            if (CaseListAttributeType.CODE.equals(attribute.getType().getCode()))
            {

                value = attribute.getType().<ru.naumen.metainfo.shared.elements.CaseListAttributeType> cast()
                        .getRelatedFqn().toString();
            }
            else
            {
                value = attribute.getType().<ru.naumen.metainfo.shared.elements.ObjectAttributeType> cast()
                        .getRelatedMetaClass().toString();
            }

            context.setProperty(AttributeFormPropertyCode.TARGET_CLASS, value);
            setRelatedMetainfo(value, callback);
        }
    }

    @Override
    public void refreshProperty(final PropertyContainerContext context, final ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        metainfoService.getAllClasses(new EditRefreshCallback(callback, context, property));
    }
}
