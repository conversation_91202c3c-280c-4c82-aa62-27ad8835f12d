package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.quickform;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.QUICK_ADD_FORM_CODE;

import jakarta.inject.Singleton;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

/**
 * Делегат обновления свойства "Форма быстрого добавления".
 * <AUTHOR>
 * @since Dec 25, 2017
 */
@Singleton
public class QuickAddFormRefreshDelegateImpl<F extends ObjectForm> extends QuickFormRefreshDelegateImpl<F>
{
    @Override
    protected String getCurrentValue(PropertyContainerContext context)
    {
        return context.getPropertyValues().getProperty(QUICK_ADD_FORM_CODE);
    }
}
