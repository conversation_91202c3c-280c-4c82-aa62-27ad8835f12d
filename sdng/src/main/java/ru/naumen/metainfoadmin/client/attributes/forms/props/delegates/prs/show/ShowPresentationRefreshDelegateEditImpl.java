package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show;

import java.util.Map;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.PresentationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обновление значения свойства "Представление для отображения" на форме редактирования атрибута
 * <AUTHOR>
 * @since 17.05.2012
 */
public class ShowPresentationRefreshDelegateEditImpl extends PresentationRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        String typeCode = attribute.getType().getCode();
        Map<String, Object> settings = getPrsSettings(typeCode, context);
        initPrsSelectList(property.getValueWidget(), attrService.getShow(typeCode, settings), settings);
        String prsCode = attribute.getViewPresentation().getCode();
        property.trySetObjValue(prsCode);
        context.setProperty(AttributeFormPropertyCode.SHOW_PRS, prsCode);
        callback.onSuccess(true);
    }
}
