package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfo.shared.Constants.TYPES_FOR_ADVLIST_SEMANTIC_FILTERING;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_ADVLIST_SEMANTIC_FILTERING;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Вычисление общих вспомогательных значений
 * <AUTHOR>
 * @since 31.05.2012
 */
public class AttributeFormContextPropertiesSetterImpl<F extends ObjectForm> implements
        AttributeFormContextPropertiesSetter<F>
{
    @Override
    public void setContextProperties(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (attrType == null)
        {
            attrType = AggregateAttributeType.CODE;
        }
        Boolean computable = context.getPropertyValues().<Boolean> getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues()
                .<Boolean> getProperty(AttributeFormPropertyCode.DETERMINABLE);
        boolean editable = !Constants.NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType);
        boolean filteredByScript = Constants.FILTERED_BY_SCRIPT_ATTRIBUTE_TYPES.contains(attrType);
        context.getContextValues().setProperty(AttributeFormPropertyCode.EDITABLE,
                editable && !computable && !determinable);
        context.getContextValues().setProperty(AttributeFormPropertyCode.FILTERED_BY_SCRIPT,
                filteredByScript && !computable && !determinable);
        context.getContextValues().setProperty(AttributeFormPropertyCode.EDITABLE_IN_LISTS, false);
        context.getContextValues().setProperty(HAS_ADVLIST_SEMANTIC_FILTERING,
                TYPES_FOR_ADVLIST_SEMANTIC_FILTERING.contains(attrType));
    }
}
