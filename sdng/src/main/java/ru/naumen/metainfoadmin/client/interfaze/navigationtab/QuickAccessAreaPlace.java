package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.common.base.Preconditions;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Плейс области панели быстрого доступа
 * <AUTHOR>
 * @since 21.09.2020
 */
public class QuickAccessAreaPlace extends Place
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<QuickAccessAreaPlace>
    {
        @Override
        public QuickAccessAreaPlace getPlace(String token)
        {
            Preconditions.checkNotNull(token, "Bad QuickAccessAreaPlace");
            return new QuickAccessAreaPlace(token);
        }

        @Override
        public String getToken(QuickAccessAreaPlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "quick-access-tile";

    private String code = null;
    private QuickAccessPanelAreaSettingsDTO area = null;

    protected QuickAccessAreaPlace()
    {
    }

    public QuickAccessAreaPlace(QuickAccessPanelAreaSettingsDTO area)
    {
        this.area = area;
        this.code = area.getCode();
    }

    public QuickAccessAreaPlace(String code)
    {
        this.code = code;
    }

    public QuickAccessPanelAreaSettingsDTO getArea()
    {
        return area;
    }

    public String getCode()
    {
        return code;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        QuickAccessAreaPlace other = (QuickAccessAreaPlace)obj;
        return ObjectUtils.equals(this.code, other.code);
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }

    @Override
    public String toString()
    {
        return "QuickAccessAreaPlace: " + getCode();
    }
}