package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms;

import static ru.naumen.metainfo.shared.Constants.HomePage.*;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.ATTRIBUTE_CHAIN;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CASES;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CLASS;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.REFERENCE_VALUE;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Базовый класс создания формы добавления\редактирования домашней страницы
 *
 * <AUTHOR>
 * @since 30.01.2023
 */
public abstract class HomePageFormPropertyParametersDescriptorFactoryBase<F extends ObjectForm> extends PropertyParametersDescriptorFactoryImpl<HomePageDtObject, F>
{
    @Inject
    private NavigationSettingsMessages messages;
    @Inject
    private TagsMessages tagsMessages;
    @Inject
    private AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        int pos = 0;
        //@formatter:off
        registerOrModifyProperty(TYPE, cmessages.homePageView(), true, TYPE, pos++, true, true);
        registerOrModifyProperty(TITLE, cmessages.title(), true, TITLE, pos++, true, true);
        registerOrModifyProperty(CUSTOM_LINK_VALUE, cmessages.linkAddress(), true, CUSTOM_LINK_VALUE, pos++, true, false);
        registerOrModifyProperty(PROFILES, messages.profiles(), true, PROFILES, pos++, true, true);
        registerOrModifyProperty(REFERENCE_CARD_TYPE, messages.contentLinkToCard(), true, REFERENCE_CARD_TYPE, pos++, true, true);
        registerOrModifyProperty(ATTRIBUTE_CHAIN, messages.contentLinkAttribute(), false, ATTRIBUTE_CHAIN, pos++, true, true);
        registerOrModifyProperty(OBJECT_CLASS, messages.contentObjectClass() , false, OBJECT_CLASS, pos++, true, true);
        registerOrModifyProperty(OBJECT_CASES , messages.contentObjectType(), false, OBJECT_CASES, pos++, true, true);
        registerOrModifyProperty(REFERENCE_VALUE, messages.contentCard(), true, REFERENCE_VALUE, pos++, true, false);
        registerOrModifyProperty(REFERENCE_TAB_VALUE, messages.contentContent(), false, REFERENCE_TAB_VALUE, pos++, true, true);
        registerOrModifyProperty(REFERENCE_UI_TEMPLATE, messages.cardTemplate(), false, REFERENCE_UI_TEMPLATE, pos++, true, true);
        registerOrModifyProperty(TAGS, tagsMessages.tags(), false, TAGS, pos++, true, true);
        registerOrModifyProperty(SETTINGS_SET, adminDialogMessages.settingsSet(), false, SETTINGS_SET, pos, true, false);
        //@formatter:on
    }
}
