package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidecaption;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат, осуществляющий биндинг свойства "Скрывать название атрибута"
 * <AUTHOR>
 * @since 02.03.2020
 */
public class HideAttrCaptionBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<Boolean, BooleanCheckBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    AttributesMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Void> callback)
    {
        property.setCaption(messages.hideCaptionAttribute());
        property.ensureDebugId(AttributeFormPropertyCode.HIDDEN_ATTR_CAPTION);
        super.bindProperty(context, property, callback);
    }
}
