package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.edit;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.INHERIT;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.PresentationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обновление значения свойства "Представление для редактирования" на форме редактирования атрибута
 * <AUTHOR>
 * @since 17.05.2012
 */
public class EditPresentationRefreshDelegateEditImpl extends PresentationRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    public void refreshProperty(final PropertyContainerContext context, final ListBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        Attribute attribute = context.getContextValues().<Attribute> getProperty(AttributeFormContextValues.ATTRIBUTE);
        String typeCode = attribute.getType().getCode();
        Map<String, Object> settings = getPrsSettings(typeCode, context);
        List<String> editPrs = Lists.newArrayList(attrService.getEdit(typeCode, settings));
        if (Boolean.FALSE.equals(context.getPropertyValues().<Boolean> getProperty(AttributeFormPropertyCode.EDITABLE)))
        {
            editPrs.remove(Presentations.STRING_EDIT_WITH_MASK);
        }
        String editPrsCode = SelectListPropertyValueExtractor.getValue(property);
        if (!editPrs.contains(editPrsCode))
        {
            editPrsCode = attribute.getEditPresentation().getCode();
        }
        if (!editPrs.contains(editPrsCode))
        {
            editPrsCode = attrService.getEditDef(typeCode, settings);
        }
        initPrsSelectList(property.getValueWidget(), editPrs.toArray(new String[editPrs.size()]), settings);
        context.getPropertyValues().setProperty(AttributeFormPropertyCode.EDIT_PRS, editPrsCode);
        property.trySetObjValue(editPrsCode, false);

        Boolean inherit = context.getPropertyValues().getProperty(INHERIT);
        Boolean hasEditPrs = !inherit;
        property.setEnabled(hasEditPrs);

        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        context.getRefreshProcess().startCustomProcess(
                Arrays.asList(AttributeFormPropertyCode.INPUTMASK_MODE, AttributeFormPropertyCode.INPUTMASK,
                        AttributeFormPropertyCode.DEFAULT_VALUE));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();

        if (attribute.getType().isAttributeOfRelatedObject())
        {
            callback.onSuccess(false);
        }
        else
        {
            callback.onSuccess(!computable && !determinable);
        }
    }
}
