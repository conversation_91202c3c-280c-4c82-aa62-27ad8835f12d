package ru.naumen.metainfoadmin.client.templates.content.command;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Реестр команд для управления шаблонами контентов в интерфейсе администратора.
 * <AUTHOR>
 * @since Mar 25, 2021
 */
@Singleton
public class ContentTemplateCommandFactoryInitializer
{
    @Inject
    public ContentTemplateCommandFactoryInitializer(CommandFactory commandFactory,
            CommandProvider<EditContentTemplateCommand, CommandParam<DtObject, DtObject>> editProvider,
            CommandProvider<DeleteContentTemplateCommand, CommandParam<DtObject, Void>> deleteProvider,
            CommandProvider<DeleteContentTemplatesCommand, CommandParam<Collection<DtObject>, Void>> deleteMassProvider)
    {
        commandFactory.register(ContentTemplateCommandCode.EDIT, editProvider);
        commandFactory.register(ContentTemplateCommandCode.DELETE, deleteProvider);
        commandFactory.register(ContentTemplateCommandCode.DELETE_MASS, deleteMassProvider);
    }
}
