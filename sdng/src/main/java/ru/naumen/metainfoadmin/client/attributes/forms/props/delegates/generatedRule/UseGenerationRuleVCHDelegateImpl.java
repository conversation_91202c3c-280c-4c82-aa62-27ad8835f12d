package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.List;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 10 дек. 2013 г.
 */
public class UseGenerationRuleVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean useGR = context.getPropertyValues().getProperty(USE_GEN_RULE);
        if (!useGR)
        {
            context.setProperty(GEN_RULE, StringUtilities.EMPTY);
            context.setProperty(EDITABLE, true);
        }
        else
        {
            context.setProperty(COMPUTABLE, false);
            context.setProperty(DETERMINABLE, false);
            context.setProperty(EDITABLE, false);
            context.setProperty(EDITABLE_IN_LISTS, false);
            context.setProperty(COMPUTABLE_ON_FORM, false);
        }
        context.setPropertyEnabled(UNIQUE, !useGR);
        List<String> refreshProcess = Lists.newArrayList(COMPUTABLE, DETERMINABLE, EDITABLE, EDITABLE_IN_LISTS,
                GEN_RULE, UNIQUE, COMPOSITE);
        if (StringAttributeType.CODE.equals(context.getPropertyValues().getProperty(ATTR_TYPE)))
        {
            refreshProcess.add(EDIT_PRS);
        }
        context.getRefreshProcess().startCustomProcess(refreshProcess);
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
