package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;

import com.google.common.collect.ImmutableMap;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ContentLinkConstants;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Компонент для извлечения свойств, необходимых для построения окружения быстрого добавления или фильтрации шаблонов
 * контентов.
 * <AUTHOR>
 * @since Apr 14, 2021
 */
public class ContentTemplatePropertiesTranslator
{
    private static final ImmutableMap<String, String> PROPERTY_MAP = ImmutableMap.<String, String> builder()
            .put(MenuItemLinkToContentCode.CONTENT_TYPE, ContentTemplate.CONTENT_TYPE)
            .put(MenuItemLinkToContentCode.HIERARCHY_STRUCTURE,
                    HierarchyGrid.class.getSimpleName() + '.' + ContentLinkConstants.HierarchyGrid.STRUCTURE_CODE)
            .build();

    public static IProperties translate(IProperties properties)
    {
        IProperties result = new MapProperties();
        PROPERTY_MAP.forEach((key, value) -> result.setProperty(value, properties.getProperty(key)));
        String linkObject = properties.getProperty(MenuItemLinkToContentCode.LINK_OBJECT);
        if (CURRENT_USER.equals(linkObject))
        {
            result.setProperty(ContentLinkConstants.CARD_OBJECT_UUID, CURRENT_USER);
            result.setProperty(ContentLinkConstants.CARD_OBJECT_FQN, Employee.FQN);
        }
        else if (OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
        {
            StringBuilder builder = new StringBuilder(CURRENT_USER);
            RelationsAttrTreeObject linkAttr = properties.getProperty(MenuItemLinkToContentCode.LINK_OBJECT_ATTR);
            if (null != linkAttr)
            {
                Collection<AttrReference> attrChain = Objects.requireNonNull(transformAttrTreeDtoToAttrChain(linkAttr));
                attrChain.forEach(attr ->
                {
                    builder.append('.');
                    builder.append(AttributeFqn.create(attr.getClassFqn(), attr.getAttrCode()));
                });
            }

            result.setProperty(ContentLinkConstants.CARD_OBJECT_UUID, builder.toString());
        }
        else if (Root.FQN.getId().equals(linkObject))
        {
            result.setProperty(ContentLinkConstants.CARD_OBJECT_FQN, Root.FQN);
        }
        else
        {
            result.setProperty(ContentLinkConstants.CARD_OBJECT_UUID,
                    properties.getProperty(MenuItemLinkToContentCode.LINK_OBJECT_UUID));
        }
        return result;
    }

    @Nullable
    public static List<AttrReference> transformAttrTreeDtoToAttrChain(@Nullable RelationsAttrTreeObject ato)
    {
        if (null == ato)
        {
            return null;
        }
        ArrayList<AttrReference> attrChain = new ArrayList<>();
        for (; ato != null; ato = ato.getParent())
        {
            Attribute attribute = ato.getAttribute();
            if (null != attribute)
            {
                AttrReference ar = new AttrReference(attribute.getMetaClassLite().getFqn(), attribute.getCode());
                attrChain.add(0, ar);
            }
        }
        return attrChain;
    }
}
