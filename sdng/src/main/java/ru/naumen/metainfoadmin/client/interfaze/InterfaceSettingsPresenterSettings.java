package ru.naumen.metainfoadmin.client.interfaze;

import jakarta.inject.Singleton;

/**
 * <AUTHOR>
 * @since Jan 10, 2014
 */
@Singleton
public class InterfaceSettingsPresenterSettings
{
    private boolean withNavigation = true;
    private boolean withAdminTheme = true;
    private boolean withUserThemes = true;

    public boolean isWithAdminTheme()
    {
        return withAdminTheme;
    }

    public boolean isWithNavigation()
    {
        return withNavigation;
    }

    public boolean isWithUserThemes()
    {
        return withUserThemes;
    }

    public void setWithAdminTheme(boolean withAdminTheme)
    {
        this.withAdminTheme = withAdminTheme;
    }

    public void setWithNavigation(boolean withNavigation)
    {
        this.withNavigation = withNavigation;
    }

    public void setWithUserThemes(boolean withUserThemes)
    {
        this.withUserThemes = withUserThemes;
    }
}
