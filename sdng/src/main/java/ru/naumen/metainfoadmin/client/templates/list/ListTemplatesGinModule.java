package ru.naumen.metainfoadmin.client.templates.list;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.UsageListTemplateGinModule;
import ru.naumen.metainfoadmin.client.templates.list.commands.DelListTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.list.commands.EditListTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.list.commands.EditParametersListTemplateCommand;

/**
 * <AUTHOR>
 * @since 19.04.2018
 */
public class ListTemplatesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new UsageListTemplateGinModule());

        bind(ListTemplatesCommandFactoryInitializer.class).asEagerSingleton();
        configureCommands();
    }

    protected void configureCommands()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditListTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<EditListTemplateCommand, CommandParam<DtObject, DtObject>>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DelListTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<DelListTemplateCommand, CommandParam<Collection<DtObject>, Void>>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditParametersListTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<EditParametersListTemplateCommand, CommandParam<DtObject, DtObject>>>(){}));
        //@formatter:on
    }
}
