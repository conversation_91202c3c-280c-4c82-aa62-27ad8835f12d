package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.inject.Singleton;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Логика обработки изменения значения свойства "Начинается с url системы"
 * <AUTHOR>
 * @since 14 окт. 2021 г.
 */
@Singleton
public class CustomSystemLinkValuePropertyVCHImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(MenuItemPropertyCode.CUSTOM_LINK_VALUE).refresh();
    }
}