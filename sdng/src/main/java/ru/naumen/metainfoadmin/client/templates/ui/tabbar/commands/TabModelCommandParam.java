package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabModel;

/**
 * Параметр команды для управления вкладками в шаблоне.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class TabModelCommandParam extends CommandParam<TabModel, Void>
{
    private final UIContext context;
    private final List<TabModel> tabs;

    public TabModelCommandParam(@Nullable TabModel value, @Nullable AsyncCallback<Void> callback,
            UIContext context, List<TabModel> tabs)
    {
        super(value, callback);
        this.context = context;
        this.tabs = tabs;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <V extends FactoryParam<TabModel, Void>> V cloneIt()
    {
        return (V)new TabModelCommandParam(getValue(), getCallback(), getContext(), getTabs());
    }

    public UIContext getContext()
    {
        return context;
    }

    public List<TabModel> getTabs()
    {
        return tabs;
    }
}
