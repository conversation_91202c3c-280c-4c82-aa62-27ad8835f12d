package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.PRESENTATION;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;

/**
 * Делегат обновления свойства "Иконка" элемента левого меню
 * <AUTHOR>
 * @since 25.06.2020
 */
@Singleton
public class MenuSettingsIconDelegateRefreshImpl
        implements PropertyDelegateRefresh<SelectItem, DtObjectSelectProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Boolean> callback)
    {
        String presStr = context.getPropertyValues().getProperty(PRESENTATION);
        if (StringUtilities.isEmpty(presStr))
        {
            callback.onSuccess(false);
            return;
        }
        MenuIconType presentation = MenuIconType.valueOf(presStr);

        callback.onSuccess(MenuIconType.CATALOG_FONT_ICON.equals(presentation));
    }
}