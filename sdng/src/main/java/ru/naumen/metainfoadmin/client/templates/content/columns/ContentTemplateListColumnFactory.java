package ru.naumen.metainfoadmin.client.templates.content.columns;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate.Attributes;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.client.mode.active.extended.advlist.AdvListAttributeColumnFactoryActiveImpl;

/**
 * Фабрика колонок для списка шаблонов контентов.
 * <AUTHOR>
 * @since Apr 05, 2021
 */
public class ContentTemplateListColumnFactory extends AdvListAttributeColumnFactoryActiveImpl
{
    private final Provider<ContentTemplateSummaryColumn> summaryColumnProvider;

    @Inject
    public ContentTemplateListColumnFactory(Provider<ContentTemplateSummaryColumn> summaryColumnProvider)
    {
        this.summaryColumnProvider = summaryColumnProvider;
    }

    @Override
    protected Column<DtObject, ?> createColumn(Attribute attribute, String prsCode)
    {
        if (Attributes.SUMMARY.equals(attribute.getFqn()))
        {
            ContentTemplateSummaryColumn column = summaryColumnProvider.get();
            column.setSortable(true);
            return column;
        }
        else
        {
            return super.createColumn(attribute, prsCode);
        }
    }
}
