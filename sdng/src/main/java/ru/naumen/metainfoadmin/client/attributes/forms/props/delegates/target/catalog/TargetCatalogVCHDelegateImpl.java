package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.catalog;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Делегат изменения значения атрибута "Справочник"
 * <AUTHOR>
 * @since 23.09.19
 * @param <F>
 */
public class TargetCatalogVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(RELATED_ATTRS_TO_EXPORT).refresh();
        context.getPropertyControllers().get(DEFAULT_VALUE).refresh();
    }
}