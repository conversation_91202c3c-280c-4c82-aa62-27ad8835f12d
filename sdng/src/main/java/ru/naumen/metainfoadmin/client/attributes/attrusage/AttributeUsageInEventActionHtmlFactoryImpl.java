package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.eventaction.EventActionPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInEventAction;

/**
 * Представление для отображения значения места использования "Действие по событию" на форме
 * "Используется в настройках" в таблице атрибутов
 *
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInEventActionHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInEventAction>
{
    private final Formatters formatters;
    private final PlaceHistoryMapper historyMapper;

    @Inject
    public AttributeUsageInEventActionHtmlFactoryImpl(Formatters formatters,
            PlaceHistoryMapper historyMapper)
    {
        this.formatters = formatters;
        this.historyMapper = historyMapper;
    }

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInEventAction usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToEventActionCard(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToEventActionCard(AttributeUsageInEventAction usage)
    {
        EventActionPlace eventActionPlace = new EventActionPlace(usage.getCode());
        //@formatter:off
        return new Hyperlink(
                    usage.getTitle(),
                    StringUtilities.getHrefByToken(historyMapper.getToken(eventActionPlace)));
        //@formatter:on
    }

}