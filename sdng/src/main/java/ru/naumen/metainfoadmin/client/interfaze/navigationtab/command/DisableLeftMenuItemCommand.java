package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.dispatch.SwitchLeftNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Команда выключения элемента левого меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class DisableLeftMenuItemCommand extends DisableMenuItemCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "disableLeftMenuItemCommand";

    @Inject
    public DisableLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param)
    {
        super(param);
    }

    @Override
    protected SwitchNavigationMenuItemAction getAction()
    {
        return new SwitchLeftNavigationMenuItemAction();
    }
}
