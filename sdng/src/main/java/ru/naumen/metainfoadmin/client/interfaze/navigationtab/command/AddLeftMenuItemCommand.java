package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.LeftMenuItemFormPresenter;

/**
 * Кодманда добавления элемента левого меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class AddLeftMenuItemCommand extends AddMenuItemCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "addLeftMenuItemCommand";

    @Inject
    public AddLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param,
            Provider<LeftMenuItemFormPresenter<ObjectFormAdd>> addMenuItemFormProvider)
    {
        super(param, addMenuItemFormProvider);
    }
}
