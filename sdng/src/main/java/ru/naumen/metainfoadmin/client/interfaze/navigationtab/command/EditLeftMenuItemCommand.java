package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.LeftMenuItemFormPresenter;

/**
 * Команда редактирования элемента левого меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class EditLeftMenuItemCommand extends EditMenuItemCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "editLeftMenuItemCommand";

    @Inject
    public EditLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param,
            Provider<LeftMenuItemFormPresenter<ObjectFormEdit>> editMenuItemFormProvider)
    {
        super(param, editMenuItemFormProvider);
    }
}
