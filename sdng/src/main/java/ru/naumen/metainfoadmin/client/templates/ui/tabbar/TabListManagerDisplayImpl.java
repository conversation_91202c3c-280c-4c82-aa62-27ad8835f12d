package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import jakarta.inject.Inject;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.CellTableWithRowsId;
import ru.naumen.core.client.components.table.TableWithToolPanelDisplayImpl;
import ru.naumen.core.client.listeditor.ListEditorResources;

/**
 * Компоновка редактора списка вкладок для шаблона панели вкладок.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class TabListManagerDisplayImpl extends TableWithToolPanelDisplayImpl<TabModel>
        implements TabListManagerDisplay
{
    @Inject
    public TabListManagerDisplayImpl(AdminWidgetResources resources,
            CellTableWithRowsId<TabModel, ListEditorResources> table)
    {
        super(table);
        table.setRowStyles((row, rowIndex) -> resources.tables().tableRow());
        getToolBar().addStyleName(resources.buttons().gButtonsLeft());
        getToolBar().asWidget().setVisible(false);

        moveToolBarToContainer();
        setCaptionVisible(false);
    }
}
