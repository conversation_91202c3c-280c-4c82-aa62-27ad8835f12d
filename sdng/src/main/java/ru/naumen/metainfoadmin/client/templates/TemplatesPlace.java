package ru.naumen.metainfoadmin.client.templates;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.client.activity.AbstractTreePlace;

/**
 * <AUTHOR>
 * @since 06.04.2018
 */
public class TemplatesPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<TemplatesPlace>
    {

        @Override
        public TemplatesPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(TemplatesPlace place)
        {
            return "";
        }
    }

    public static final String PLACE_PREFIX = "templates";

    public static final TemplatesPlace INSTANCE = new TemplatesPlace();

    public TemplatesPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "TemplatesPlace";
    }
}