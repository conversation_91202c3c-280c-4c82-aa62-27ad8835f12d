package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventcleaner.rule.dispatch.GetEventCategoriesForFiltrationAction;

/**
 * Фабрика виджета для фильтрации по видам событий в списке правил хранения логов событий
 * <AUTHOR>
 * @since 13.07.2023
 **/
public class AttributeEventStorageRuleEventCategoriesWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;
    @Inject
    private DispatchAsync dispatch;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        dispatch.execute(new GetEventCategoriesForFiltrationAction(), new BasicCallback<SimpleResult<List<DtObject>>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<List<DtObject>> value)
            {
                SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

                List<DtObject> events = new ArrayList<>();
                for (DtObject dtObject : value.get())
                {
                    events.add(new SimpleDtObject(dtObject.getUUID(), dtObject.getTitle(), ClassFqn.parse("")));
                }
                events.sort(ITitled.COMPARATOR);
                widget.addItems(events);
                callback.onSuccess(widget);
            }
        });
    }
}