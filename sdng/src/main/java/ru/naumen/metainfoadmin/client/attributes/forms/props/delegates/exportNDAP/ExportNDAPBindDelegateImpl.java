package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;

/**
 * Делегат, осуществляющий биндинг свойства "Доступен из системы мониторинга".
 * Устанавливает подсказку у свойства.
 *
 * <AUTHOR>
 * @since 19.05.2022
 */
public class ExportNDAPBindDelegateImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<Boolean, BooleanCheckBoxInfoProperty>
        implements AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxInfoProperty>
{
    @Inject
    private AttributesMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, BooleanCheckBoxInfoProperty property,
            AsyncCallback<Void> callback)
    {
        property.setInfo(messages.syncOnlyWhenObjectUpdated());
        super.bindProperty(context, property, callback);
    }
}
