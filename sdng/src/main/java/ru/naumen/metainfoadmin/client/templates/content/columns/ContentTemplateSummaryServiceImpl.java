package ru.naumen.metainfoadmin.client.templates.content.columns;

import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate.HierarchyGrid;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.FlowContent;

/**
 * Реализация сервиса для генерации описания основных параметров для шаблона.
 * <AUTHOR>
 * @since Apr 05, 2021
 */
@Singleton
public class ContentTemplateSummaryServiceImpl implements ContentTemplateSummaryService
{
    private final Map<String, ContentTemplateSummaryFactory<?>> registry = new HashMap<>();

    @Inject
    public ContentTemplateSummaryServiceImpl(
            HierarchyGridSummaryFactory hierarchyGridSummaryFactory)
    {
        registry.put(HierarchyGrid.class.getSimpleName(), hierarchyGridSummaryFactory);
    }

    @Override
    public SafeHtml generateSummary(DtObject contentTemplateDto)
    {
        ContentTemplate template = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.TEMPLATE);
        if (null == template)
        {
            return SafeHtmlUtils.EMPTY_SAFE_HTML;
        }
        String contentType = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.CONTENT_TYPE);
        FlowContent content = template.getTemplate();
        @SuppressWarnings("unchecked")
        ContentTemplateSummaryFactory<FlowContent> factory = (ContentTemplateSummaryFactory<FlowContent>)registry
                .get(contentType);
        if (null == factory)
        {
            return SafeHtmlUtils.EMPTY_SAFE_HTML;
        }
        return factory.createSummary(contentTemplateDto, content);
    }
}
