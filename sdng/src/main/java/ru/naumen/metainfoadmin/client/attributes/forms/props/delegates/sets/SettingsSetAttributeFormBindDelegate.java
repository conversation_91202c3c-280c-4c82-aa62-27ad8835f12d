package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sets;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат привязки поля "Комплект" на формах добавления редактирования атрибута
 *
 * <AUTHOR>
 * @since 06.02.2024
 */
public class SettingsSetAttributeFormBindDelegate<F extends ObjectForm>
        implements AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxWithEmptyOptProperty>
{
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Void> callback)
    {
        String settingsSetCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.SETTINGS_SET);
        settingsSetOnFormCreator.fillSettingsSets(settingsSetCode, property);
        callback.onSuccess(null);
    }
}
