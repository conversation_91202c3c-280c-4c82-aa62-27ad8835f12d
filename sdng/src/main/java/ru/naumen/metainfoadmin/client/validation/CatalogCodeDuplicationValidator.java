package ru.naumen.metainfoadmin.client.validation;

import java.util.Set;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;

/**
 * <AUTHOR>
 * @since 18.10.2012
 *
 */
public class CatalogCodeDuplicationValidator implements Validator<String>
{
    private Set<String> existingCodes;
    private final CatalogMessages messages;
    private final AdminMetainfoServiceAsync metainfoService;

    public CatalogCodeDuplicationValidator(CatalogMessages messages, AdminMetainfoServiceAsync metainfoService)
    {
        this.messages = messages;
        this.metainfoService = metainfoService;
    }

    @Override
    public boolean validate(HasValueOrThrow<String> hasValue)
    {
        if (existingCodes != null && existingCodes.contains(hasValue.getValue()) && hasValue instanceof HasValidation)
        {
            ((HasValidation)hasValue).addValidationMessage(messages.catalogCodeDuplication());
            return false;
        }
        return true;
    }

    @Override
    public void validateAsync(HasValueOrThrow<String> hasValue, ValidateEvent event)
    {
        validate(hasValue);
    }

    void refreshExistingCodes()
    {
        metainfoService.getCatalogCodes(new BasicCallback<Set<String>>()
        {
            @Override
            protected void handleSuccess(Set<String> value)
            {
                existingCodes = value;
            }
        });
    }
}