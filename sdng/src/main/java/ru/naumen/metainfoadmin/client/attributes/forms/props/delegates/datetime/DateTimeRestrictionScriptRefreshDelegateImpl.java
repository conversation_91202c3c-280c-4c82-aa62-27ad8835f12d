package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.core.client.attr.DateTimeRestrictionAttributeClientTool.getRestrictionType;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EDITABLE;

import java.util.Objects;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 26 сент. 2018 г.
 *
 */
public class DateTimeRestrictionScriptRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, ScriptComponentEditProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean editable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(EDITABLE));
        boolean isDateTimeProperty = Constants.DATE_TIME_TYPES.contains(attrType);
        boolean isRestrictedByScript = false;
        if (isDateTimeProperty)
        {
            RestrictionType dateTimeRestrictionType = getRestrictionType(context);
            if (RestrictionType.NO_RESTRICTION == dateTimeRestrictionType)
            {
                context.setProperty(DATE_TIME_RESTRICTION_SCRIPT, null);
            }
            isRestrictedByScript = Objects.equals(RestrictionType.RESTRICTION_BY_SCRIPT, dateTimeRestrictionType);
        }
        callback.onSuccess(editable && isDateTimeProperty && isRestrictedByScript);
    }

}
