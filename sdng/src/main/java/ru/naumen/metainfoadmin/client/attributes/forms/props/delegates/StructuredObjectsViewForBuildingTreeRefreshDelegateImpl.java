package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Делегат обновления поля "Структура для построения дерева".
 *
 * <AUTHOR>
 * @since 04.04.2025
 */
public class StructuredObjectsViewForBuildingTreeRefreshDelegateImpl<F extends ObjectForm>
        extends StructureBaseRefreshDelegate<F>
{
    @Inject
    protected StructuredObjectsViewForBuildingTreeRefreshDelegateImpl(DispatchAsync dispatch)
    {
        super(dispatch);
    }

    @Override
    protected boolean isShowStructuredObjectsViewField(PropertyContainerContext context)
    {
        IProperties propertyValues = context.getPropertyValues();

        String attrType = propertyValues.getProperty(ATTR_TYPE);
        if (Boolean.FALSE.equals(LINK_ATTRIBUTE_TYPES.contains(attrType)))
        {
            return false;
        }

        String editPresentations = propertyValues.getProperty(EDIT_PRS);
        return Presentations.STRUCTURE_BASED_EDIT_PRS.contains(editPresentations);
    }
}