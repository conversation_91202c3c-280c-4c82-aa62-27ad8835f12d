package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode.FQNS;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassSameClassOnlyTreeContext;
import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule;

/**
 * Контроллер для свойств вида "Класс объектов для добавления"
 * <AUTHOR>
 * @since 15.12.2021
 */
public class AddButtonSettingsTreePropertyControllerImpl extends PropertyControllerImpl<Collection<DtObject>,
        PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                MetaClassMultiSelectionModelSameClassOnly>>>
{
    @Inject
    private DtoMetaClassesTreeFactory<MetaClassMultiSelectionModelSameClassOnly,
            DtoMetaClassSameClassOnlyTreeContext> treeModelHelper;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> treeFactory;
    @Inject
    private CommonMessages cmessages;

    @Inject
    protected AddButtonSettingsTreePropertyControllerImpl(
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<Collection<DtObject>, PropertyBase<Collection<DtObject>,
                    PopupValueCellTree<DtObject, Collection<DtObject>,
                            MetaClassMultiSelectionModelSameClassOnly>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    @Override
    public void refresh()
    {
        boolean isVisible = context.getPropertyValues()
                .getProperty(EditNavigationSettingsFormGinModule.MenuItemPropertyCode.TYPE)
                .equals(IMenuItem.MenuItemType.addButton.name());
        if (isVisible)
        {
            ITreeViewModel<DtObject, MetaClassMultiSelectionModelSameClassOnly> treeViewModel = treeModelHelper
                    .createMetaClassTreeViewModel(
                            Container.create(new DtoMetaClassSameClassOnlyTreeContext(Constants.AbstractBO.FQN,
                                    MetaClassFilters.and(MetaClassFilters.isNotSystem(),
                                            MetaClassFilters.not(MetaClassFilters
                                                    .equal(Constants.Root.FQN))))));
            PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> cellTree =
                    treeFactory.create(treeViewModel);
            property = new PropertyBase<>(cmessages.objects(), cellTree);
            bindProperty();
            if (context.getContextValues().getProperty(FQNS) != null)
            {
                property.setValue(context.getContextValues().getProperty(FQNS));
            }
            else
            {
                property.getValue().clear();
            }
            super.refresh();
        }
        else
        {
            removeProperty();
            new DefaultRefreshCallback().onSuccess(false);
        }
    }
}
