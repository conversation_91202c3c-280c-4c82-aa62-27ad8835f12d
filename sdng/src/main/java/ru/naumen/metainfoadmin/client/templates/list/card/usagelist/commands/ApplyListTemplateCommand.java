package ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateUsagePointsAction;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateUsagePointsResponse;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;

/**
 * Команда применения изменений шаблона списка
 * <AUTHOR>
 * @since 08.08.2018
 */
public class ApplyListTemplateCommand extends BaseCommandImpl<Collection<DtObject>, Void>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Dialogs dialogs;
    @Inject
    private ListTemplatesMessages listTemplatesMessages;

    @Inject
    public ApplyListTemplateCommand(@Assisted CommandParam<Collection<DtObject>, Void> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<Collection<DtObject>, Void> param)
    {
        List<String> usagePointsUuids = param.getValue()
                .stream()
                .map(IUUIDIdentifiable::getUUID)
                .collect(Collectors.toList());
        String templateCode = ((ListTemplateUsageCommandParam)param).getTemplateCode();
        dispatch.execute(new ApplyListTemplateUsagePointsAction(templateCode, usagePointsUuids),
                new CallbackDecorator<ApplyListTemplateUsagePointsResponse, Void>(param.getCallbackSafe())
                {
                    @Override
                    protected Void apply(ApplyListTemplateUsagePointsResponse result)
                    {
                        if (!result.getMessage().isEmpty())
                        {
                            dialogs.info(result.getMessage());
                        }
                        else
                        {
                            dialogs.info(listTemplatesMessages.applySuccessfully());
                        }
                        return null;
                    }
                });
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }
}