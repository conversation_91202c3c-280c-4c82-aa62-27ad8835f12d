package ru.naumen.metainfoadmin.client.scheduler;

import ru.naumen.core.client.activity.AbstractTreePlace;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * <AUTHOR>
 * @since 29.07.2011
 *
 */
public class SchedulerPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<SchedulerPlace>
    {

        @Override
        public SchedulerPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(SchedulerPlace place)
        {
            return "";
        }
    }

    public static final String PLACE_PREFIX = "scheduler";

    public static final SchedulerPlace INSTANCE = new SchedulerPlace();

    /**
     * Not instantiated
     */
    public SchedulerPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "SchedulerPlace";
    }
}
