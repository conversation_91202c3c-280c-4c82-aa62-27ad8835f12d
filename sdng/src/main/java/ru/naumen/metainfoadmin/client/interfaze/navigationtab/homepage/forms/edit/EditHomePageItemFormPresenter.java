package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.edit;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.ObjectFormMessages;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerAfterBindHandlerImpl;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.metainfo.client.TagActionExecutor;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.HomePageFormPresenter;
import ru.naumen.metainfoadmin.shared.homepage.EditHomePageSettingsAction;
import ru.naumen.metainfoadmin.shared.homepage.HomePageActionBase;

/**
 * Форма редактирования элемента домашней страницы
 *
 * <AUTHOR>
 * @since 10.01.2023
 */
public class EditHomePageItemFormPresenter extends HomePageFormPresenter<ObjectFormEdit>
{
    @Inject
    public EditHomePageItemFormPresenter(
            PropertyFormDisplay display,
            EventBus eventBus,
            Processor validation,
            NavigationSettingsMessages messages,
            PropertyContainerPresenterFactory containerFactory,
            PropertyControllerFactory<HomePageDtObject, ObjectFormEdit> propertyControllerFactory,
            ObjectFormMessages<ObjectFormEdit> formMessages,
            TagActionExecutor tagActionExecutor,
            ReferenceHelper referenceHelper,
            PropertyContainerAfterBindHandlerImpl afterBindHandler)
    {
        super(display, eventBus, validation, messages, containerFactory, propertyControllerFactory, formMessages,
                tagActionExecutor, referenceHelper, afterBindHandler);
    }

    @Override
    protected HomePageActionBase getHomePageAction(HomePageDtObject homePageDtObject)
    {
        return new EditHomePageSettingsAction(homePageDtObject);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        propertyContainer.getContext().setDisabled(HomePage.TYPE);
        display.display();
    }
}
