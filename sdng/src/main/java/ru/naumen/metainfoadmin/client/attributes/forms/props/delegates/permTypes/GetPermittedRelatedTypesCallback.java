package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Collection;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.core.client.common.cellopt.CellWidgetOptions;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteResponse;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 28.05.2012
 */
public class GetPermittedRelatedTypesCallback extends BasicCallback<BatchResult>
{

    private static class DtObjectFqnFilter implements Predicate<DtObject>
    {
        private final Collection<ClassFqn> fqns;

        public DtObjectFqnFilter(Collection<ClassFqn> fqns)
        {
            this.fqns = fqns;
        }

        @Override
        public boolean test(DtObject input)
        {
            return fqns.contains(input.getMetainfo());
        }
    }

    @Inject
    @Named(CellWidgetOptions.DESELECT_ALL)
    DtObject DESELECT_ALL;

    private final PropertyContainerContext context;
    private final AsyncCallback<Boolean> callback;

    @Inject
    public GetPermittedRelatedTypesCallback(@Assisted PropertyContainerContext context,
            @Assisted AsyncCallback<Boolean> callback)
    {
        this.context = context;
        this.callback = callback;
    }

    @Override
    protected void handleSuccess(BatchResult response)
    {
        GetMetaClassesLiteResponse metaClassesResponse = response.getResult(0, GetMetaClassesLiteResponse.class);
        GetPermittedRelatedTypesResponse permittedTypesResponse = response.getResult(1,
                GetPermittedRelatedTypesResponse.class);
        Collection<DtObject> initialValue = getInitialValue(permittedTypesResponse);
        if (!initialValue.contains(DESELECT_ALL))
        {
            Collection<ClassFqn> descendantFqns = Collections2.transform(metaClassesResponse.get(),
                    MetaClassLite.FQN_EXTRACTOR);
            initialValue = initialValue.stream()
                    .filter(new DtObjectFqnFilter(descendantFqns))
                    .collect(Collectors.toList());
        }
        context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES, initialValue);

        callback.onSuccess(true);
    }

    private Collection<DtObject> getInitialValue(GetPermittedRelatedTypesResponse response)
    {
        if (response.isNoOneSelected())
        {
            return Lists.newArrayList(DESELECT_ALL);
        }
        else
        {
            return Collections2.transform(response.getPermittedTypes(), DtObject.CREATE_FROM_METACLASSLITE);
        }
    }
}
