package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Делегат обновления для виджета {@link MenuItemPropertyCode#USE_ATTR_TITLE}
 *
 * <AUTHOR>
 * @since 12.03.2022
 */
public class UseAttrTitleReferenceDelegateRefreshImpl extends ReferencePropertyDelegateRefreshBase<Boolean,
        BooleanCheckBoxProperty>
{
}
