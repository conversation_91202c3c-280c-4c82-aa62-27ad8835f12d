package ru.naumen.metainfoadmin.client.templates.list.form;

import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.templates.list.dispatch.EditListTemplateAction;

/**
 * Форма редактирования шаблона списка
 * <AUTHOR>
 * @since 06.04.2018
 *
 */
public class EditListTemplateFormPresenter extends ListTemplateFormPresenterImpl
{
    @Inject
    public EditListTemplateFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        service.execute(
                new EditListTemplateAction(title.getValue(), code.getValue(),
                        Objects.requireNonNull(SelectListPropertyValueExtractor.getValue(classList)),
                        Lists.newArrayList(SelectListPropertyValueExtractor.getCollectionValue(caseList)),
                        SelectListPropertyValueExtractor.getValue(settingsSet)),
                new BasicCallback<SimpleResult<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtObject> value)
                    {
                        unbind();
                        refreshCallback.onSuccess(value.get());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingTemplate());
        code.setDisable();
        getDisplay().display();
    }

    @Override
    protected String getInitialSettingsSetValue()
    {
        return listTemplate.getProperty(ListTemplate.SETTINGS_SET);
    }

    @Override
    Collection<String> getCaseValue()
    {
        Collection<ClassFqn> cases = listTemplate.getProperty(FakeMetaClassesConstants.ListTemplate.CASE);
        return cases.stream().map(ClassFqn::toString).collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    @Override
    String getClassValue()
    {
        return ((Collection<MetaClassLite>)listTemplate.getProperty(ListTemplate.CLASS)).iterator().next().getFqn()
                .getId();
    }
}
