package ru.naumen.metainfoadmin.client.attributes.commands;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;

/**
 * Инициализирует фабрику комманд 
 * <AUTHOR>
 *
 */
public class AttributeCommandFactoryInitializer
{
    @Inject
    public AttributeCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<DelCommand, AttributeCommandParam> deleteProvider,
            CommandProvider<EditCommand, AttributeCommandParam> editProvider,
            CommandProvider<ShowUsageAttrCommand, AttributeCommandParam> showUsageProvider)
    {
        // @formatter:off
        factory.register(AttributeCommandCode.EDIT,   editProvider);
        factory.register(AttributeCommandCode.DELETE, deleteProvider);
        factory.register(AttributeCommandCode.SHOW_USAGE, showUsageProvider);
        // @formatter:on
    }
}
