package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfo.shared.Constants.HomePage;

/**
 * Делегат изменения значения свойства "Вкладка контента" элемента домашней страницы
 * {@link HomePage#REFERENCE_TAB_VALUE}
 * <AUTHOR>
 * @since 15.01.2023
 */
public class HomePageReferenceTabVCHDelegate implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(HomePage.REFERENCE_UI_TEMPLATE).refresh();
    }
}
