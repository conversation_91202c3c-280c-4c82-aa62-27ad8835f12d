package ru.naumen.metainfoadmin.client.scheduler;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDto;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * <AUTHOR>
 */
public class ExecuteScriptTaskInfoPresenter extends SchedulerTaskInfoPresenterBase<ExecuteScriptTask>
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> lastExecutionDate;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> planExecutionDate;
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_VIEW)
    Property<ScriptDto> script;

    @Inject
    public ExecuteScriptTaskInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void addProperties()
    {
        super.addProperties();
        getDisplay().add(lastExecutionDate);
        lastExecutionDate.setCaption(messages.lastExecutionDate());
        getDisplay().add(planExecutionDate);
        planExecutionDate.setCaption(messages.planExecutionDate());
        getDisplay().add(script);
        script.setCaption(messages.script());
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(lastExecutionDate, "lastExecutionDate");
        DebugIdBuilder.ensureDebugId(planExecutionDate, "planExecutionDate");
        DebugIdBuilder.ensureDebugId(script, "script");
    }

    @Override
    protected void setPropertiesValues()
    {
        super.setPropertiesValues();
        lastExecutionDate.setValue(formatters.formatDateTimeWithSeconds(task.get().getLastExecutionDate()));
        planExecutionDate.setValue(formatters.formatDateTime(task.get().getPlanDate()));
        script.setValue(((ExecuteScriptTaskDto)task.get()).getScriptDto());
    }
}
