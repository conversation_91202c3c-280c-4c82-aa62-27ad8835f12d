package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Делегат, обрабатывающий изменения значения поля "Сложная форма добавления связи"
 * <AUTHOR>
 * @since 22.09.2015
 */
public class ComplexRelationVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(
                Lists.newArrayList(COMPLEX_RELATION_ATTR_GROUP, COMPLEX_EMPLOYEE_ATTR_GROUP, COMPLEX_TEAM_ATTR_GROUP,
                        COMPLEX_OU_ATTR_GROUP, COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, EDIT_ON_COMPLEX_FORM_ONLY));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
