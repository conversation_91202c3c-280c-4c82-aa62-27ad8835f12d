package ru.naumen.metainfoadmin.client.attributes.columns;

import ru.naumen.admin.client.widgets.AdminWidgetResources;

/**
 * Тип значения атрибута
 * <AUTHOR>
 * @since 8 окт. 2018 г.
 *
 */
public class AttrValueTypeCellValueExtractor extends ByTagAndClassNameCellValueExtractor
{

    @Override
    String getTagName()
    {
        return TAG_NAME_SPAN;
    }

    @Override
    String getClassName()
    {
        return AdminWidgetResources.INSTANCE.attributeList().typeBadge();
    }

}
