package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 26.07.2012
 *
 */
public class EscalationSchemeTargetObjectsVCHDelegateImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(EscalationSchemeFormPropertyCode.TIMER).refresh();
    }
}
