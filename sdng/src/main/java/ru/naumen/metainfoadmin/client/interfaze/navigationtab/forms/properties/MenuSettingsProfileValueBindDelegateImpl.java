package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;

import java.util.Collection;

import com.google.inject.Singleton;

/**
 * Делегат биндинга свойства "Профили"
 *
 * <AUTHOR>
 * @since 27.06.2020
 */
@Singleton
public class MenuSettingsProfileValueBindDelegateImpl
        extends PropertyDelegateBindImpl<Collection<SelectItem>, MultiSelectBoxProperty>
{
}
