package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат, осуществляющий биндинг свойства "Запоминать выбранные единицы измерения" 
 * <AUTHOR>
 * @since 07.04.2017
 */
public class NeedStoreUnitsBindDelegateEditImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<Boolean, BooleanCheckBoxInfoProperty>
        implements AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxInfoProperty>
{
    @Inject
    private AttributesMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, BooleanCheckBoxInfoProperty property,
            AsyncCallback<Void> callback)
    {
        Attribute attribute = context.getContextValues().getProperty(ATTRIBUTE);
        if (attribute != null && attribute.isHardcoded())
        {
            property.setDisable();
        }
        property.setInfo(messages.needStoreUnitsInfo());
        context.getContextValues().setProperty(AttributeFormPropertyCode.NEED_STORE_UNITS,
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.NEED_STORE_UNITS));
        super.bindProperty(context, property, callback);
    }
}
