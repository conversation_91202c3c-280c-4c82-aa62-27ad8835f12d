package ru.naumen.metainfoadmin.client.templates.content.command;

import static ru.naumen.core.shared.Constants.MASS_SUFFIX;

import java.util.Set;

import com.google.common.collect.ImmutableSet;

/**
 * Коды команд управления шаблонами контентов.
 * <AUTHOR>
 * @since Mar 25, 2021
 */
public abstract class ContentTemplateCommandCode
{
    public static final String EDIT = "editContentTemplate";
    public static final String DELETE = "deleteContentTemplate";

    public static final String DELETE_MASS = DELETE + MASS_SUFFIX;

    public static final Set<String> COMMANDS_IN_LIST = ImmutableSet.of(EDIT, DELETE, DELETE_MASS);
}
