package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

public class NeedStoreUnitsVCHDelegateAddImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
    }
}
