package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode.ADD_BUTTON_VALUE;

import jakarta.inject.Inject;

import ru.naumen.admin.client.AdminMessages;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Фабрика регистрации дескрипторов атрибутов элемента левого меню
 * <AUTHOR>
 * @since 25.06.2020
 */
public class LeftMenuItemPropertyDescriptorFactoryImpl extends
        PropertyParametersDescriptorFactoryImpl<LeftMenuItemSettingsDTO, ObjectFormEdit>
{
    private final NavigationSettingsMessages messages;
    private final ContentCreatorMessages contMessages;
    private final CopyFromTemplateMessages copyFromTemplateMessages;
    private final TagsMessages tagsMessages;
    private final HierarchyGridMessages hierarchyGridMessages;
    private final EditableToolPanelMessages editableMessages;
    private final AdminMessages adminMessages;
    private final AdminDialogMessages adminDialogMessages;

    @Inject
    public LeftMenuItemPropertyDescriptorFactoryImpl(
            NavigationSettingsMessages messages,
            ContentCreatorMessages contMessages,
            CopyFromTemplateMessages copyFromTemplateMessages,
            TagsMessages tagsMessages,
            HierarchyGridMessages hierarchyGridMessages,
            EditableToolPanelMessages editableMessages,
            AdminMessages adminMessages,
            AdminDialogMessages adminDialogMessages)
    {
        this.messages = messages;
        this.contMessages = contMessages;
        this.copyFromTemplateMessages = copyFromTemplateMessages;
        this.tagsMessages = tagsMessages;
        this.hierarchyGridMessages = hierarchyGridMessages;
        this.editableMessages = editableMessages;
        this.adminMessages = adminMessages;
        this.adminDialogMessages = adminDialogMessages;
    }

    @Override
    protected void build()
    {
        int pos = 0;
        //@formatter:off
        registerOrModifyProperty(MenuItemPropertyCode.TYPE, cmessages.elementView(), true,
                MenuItemPropertyCode.TYPE, pos++, true, true);

        registerOrModifyProperty(MenuItemPropertyCode.TITLE, cmessages.title(), true,
                MenuItemPropertyCode.TITLE, pos++, true, true);

        registerOrModifyProperty(MenuItemPropertyCode.USE_ATTR_TITLE, messages.contentUseAttrTitle(),false,
                MenuItemPropertyCode.USE_ATTR_TITLE, pos++, true, true);
        registerOrModifyProperty(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, messages.contentAttrTitle(), true,
                MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, pos++, true, true);

        registerOrModifyProperty(MenuItemPropertyCode.PARENT, messages.insertedInChapter(), false,
                MenuItemPropertyCode.PARENT, pos++, true, true);
        registerOrModifyProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE, cmessages.startWithSystemUrl(), false,
                MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE, pos++, false,  false);
        registerOrModifyProperty(MenuItemPropertyCode.CUSTOM_LINK_VALUE, cmessages.linkAddress(), true,
                MenuItemPropertyCode.CUSTOM_LINK_VALUE, pos++, false, false);
        registerOrModifyProperty(MenuSettingsPropertyCode.PROFILES, messages.profiles(), false,
                MenuSettingsPropertyCode.PROFILES, pos++, false,true);

        registerOrModifyProperty(MenuItemPropertyCode.TYPE_OF_CARD, messages.contentLinkToCard(), true,
                MenuItemPropertyCode.TYPE_OF_CARD, pos++, true, true);

        registerOrModifyProperty(ReferenceCode.ATTRIBUTE_CHAIN, messages.contentLinkAttribute(), false,
                "menuItemAttrChain", pos++, true, true);
        registerOrModifyProperty(ReferenceCode.OBJECT_CLASS, messages.contentObjectClass() , false,
                "menuItemObjectClass", pos++, true, true);
        registerOrModifyProperty(ReferenceCode.OBJECT_CASES , messages.contentObjectType(), false,
                "menuItemObjectCases", pos++, true, true);

        registerOrModifyProperty(ReferenceCode.REFERENCE_VALUE, messages.contentCard(), false,
                "menuItemValueCard", pos++, false, false);
        registerOrModifyProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE, messages.contentContent(), false,
                "menuItemValueContent", pos++, false, false);
        registerOrModifyProperty(MenuItemPropertyCode.REFERENCE_UI_TEMPLATE, messages.cardTemplate(), false,
                "menuItemValueTemplate", pos++, false, false);

        registerOrModifyProperty(MenuItemLinkToContentCode.CONTENT_TYPE, messages.contentType(), true,
                MenuItemLinkToContentCode.CONTENT_TYPE,pos++, false, false);

        registerOrModifyProperty( MenuItemLinkToContentCode.LINK_OBJECT, messages.linkObject(), true,
                MenuItemLinkToContentCode.LINK_OBJECT, pos++,false, false);
        registerOrModifyProperty( MenuItemLinkToContentCode.LINK_OBJECT_CASE, messages.currentUserType(), false,
                MenuItemLinkToContentCode.LINK_OBJECT_CASE, pos++, false,false);
        registerOrModifyProperty( MenuItemLinkToContentCode.LINK_OBJECT_ATTR, messages.linkObjectAttribute(), false,
                MenuItemLinkToContentCode.LINK_OBJECT_ATTR, pos++, false, false);
        registerOrModifyProperty( MenuItemLinkToContentCode.LINK_OBJECT_UUID, messages.linkObjectUUID(), false,
                MenuItemLinkToContentCode.LINK_OBJECT_UUID, pos++, false, false);

        registerOrModifyProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY, contMessages.showRelatedWithNested(),false,
                MenuItemLinkToContentCode.SHOW_HIERARCHY, pos++, false, false);

        registerOrModifyProperty(MenuItemLinkToContentCode.ATTR_CHAIN, cmessages.attribute(), false,
                MenuItemLinkToContentCode.ATTR_CHAIN, pos++,false, false);

        registerOrModifyProperty(MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR, contMessages.beforeHierarchy(), false,
                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR, pos++,false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.HIERARCHY_CLASS, contMessages.hierarchyClass(), false,
                MenuItemLinkToContentCode.HIERARCHY_CLASS, pos++, false,false);
        registerOrModifyProperty(MenuItemLinkToContentCode.NESTED_ATTR_LINK, contMessages.hierarchyAttribute(), false,
                MenuItemLinkToContentCode.NESTED_ATTR_LINK, pos++,false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR, contMessages.afterHierarchy(), false,
                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR, pos++,false, false);

        registerOrModifyProperty(MenuItemLinkToContentCode.OBJECT_CLASS , contMessages.objectClass(), false,
                "objectClass", pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.CHAIN_ATTR_CLASS , contMessages.objectClass(), false,
                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS, pos++, false,false);
        registerOrModifyProperty(MenuItemLinkToContentCode.OBJECT_CASES, contMessages.objectsTypes(), false,
                "objectCases", pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.ATTRIBUTE_GROUP, cmessages.attributeGroup(), false,
                MenuItemLinkToContentCode.ATTRIBUTE_GROUP, pos++, false,false);
        //  TODO Пока скрываем с формы, так как ждем починки дефекта NSDPRD-14869 на кнопку "Добавить связь"
        //   {$link https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107823141}
        //
        //  registerOrModifyProperty(MenuItemLinkToContentCode.ADD_LINK_FORM_TITLE, cmessages.addingLinkFormCaption(),
        //        false, MenuItemLinkToContentCode.ADD_LINK_FORM_TITLE, pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.HIERARCHY_STRUCTURE,
                hierarchyGridMessages.structuredObjectsView(), false, MenuItemLinkToContentCode.HIERARCHY_STRUCTURE,
                pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.LIST_TEMPLATE, copyFromTemplateMessages.template(), false,
                MenuItemLinkToContentCode.LIST_TEMPLATE, pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE, copyFromTemplateMessages.template(), false,
                MenuItemLinkToContentCode.CONTENT_TEMPLATE, pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.LIST_PRESENTATION, cmessages.presentation(), false,
                MenuItemLinkToContentCode.LIST_PRESENTATION, pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.SHOW_NESTED_IN_NESTED, contMessages.showNestedInNested(),false,
                MenuItemLinkToContentCode.SHOW_NESTED_IN_NESTED, pos++, false, false);
        registerOrModifyProperty(MenuItemLinkToContentCode.LIST_PAGE_TITLE, messages.listPageTitle(), false,
                MenuItemLinkToContentCode.LIST_PAGE_TITLE, pos++, false, false);
        registerOrModifyProperty(ADD_BUTTON_VALUE, cmessages.objectClassToAdd(), true,
                ADD_BUTTON_VALUE, pos++, false, true);
        registerOrModifyProperty(MenuItemPropertyCode.NEW_TAB_VALUE, cmessages.openInNewTab(), false,
                MenuItemPropertyCode.NEW_TAB_VALUE, pos++, false,  false);
        registerOrModifyProperty(ToolFormPropertyCodes.ACTION, adminMessages.action(), false,
                ToolFormPropertyCodes.ACTION, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, editableMessages.useQuickAddForm(), false,
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, editableMessages.goToCardAfterCreation(), false,
                ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.QUICK_ADD_FORM, editableMessages.quickAddForm(), false,
                ToolFormPropertyCodes.QUICK_ADD_FORM, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, editableMessages.attributeFillByCurrentObject(), false,
                ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, pos++, false, false);

        registerOrModifyProperty(MenuSettingsPropertyCode.PRESENTATION, cmessages.menuPresentation(), true,
                MenuSettingsPropertyCode.PRESENTATION, pos++, true, true);
        registerOrModifyProperty(MenuSettingsPropertyCode.ICON, cmessages.menuIcon(), true,
                MenuSettingsPropertyCode.ICON, pos++, false, true);
        registerOrModifyProperty(MenuSettingsPropertyCode.ABBREVIATION, cmessages.menuAbbreviation(), true,
                MenuSettingsPropertyCode.ABBREVIATION, pos++, true, true);
        registerOrModifyProperty(MenuItemPropertyCode.FORMATTING, messages.formatting(), true,
                MenuItemPropertyCode.FORMATTING, pos++, true, true);
        registerOrModifyProperty(MenuSettingsPropertyCode.TAGS, tagsMessages.tags(), false,
                MenuSettingsPropertyCode.TAGS, pos++, true, true);
        registerOrModifyProperty(MenuSettingsPropertyCode.SETTINGS_SET, adminDialogMessages.settingsSet(), false,
                MenuSettingsPropertyCode.SETTINGS_SET, pos, true, false);
        //@formatter:on
    }
}