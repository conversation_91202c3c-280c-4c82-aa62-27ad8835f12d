package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.core.client.attr.DateTimeRestrictionAttributeClientTool.isRestrictedByScript;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DETERMINABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EDITABLE;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ValueCellListProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Значение атрибута допустимо указывать"
 * <AUTHOR>
 * @since 11 дек. 2018 г.
 */
public class DateTimeCommonRestrictionRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, ValueCellListProperty<CommonRestriction> property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean editable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(EDITABLE));
        boolean determinable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(DETERMINABLE));
        boolean computable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean isDateTimeAttr = Constants.DATE_TIME_TYPES.contains(attrType);
        callback.onSuccess(isDateTimeAttr && editable && !computable && !determinable &&
                           !isRestrictedByScript(context));
    }

}
