package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 *
 */
public class InputmaskVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String inputmaskMode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE);
        String inputmask = context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK);
        if (StringAttributeType.INPUT_MASK_ALIAS.equals(inputmaskMode))
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK_ALIAS, inputmask);
        }
        else if (StringAttributeType.INPUT_MASK_DEFINITIONS.equals(inputmaskMode))
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK_DEFINITIONS, inputmask);
        }
        else if (StringAttributeType.INPUT_MASK_REGEX.equals(inputmaskMode))
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK_REGEX, inputmask);
        }
        context.getPropertyControllers().get(AttributeFormPropertyCode.DEFAULT_VALUE).refresh();
    }
}