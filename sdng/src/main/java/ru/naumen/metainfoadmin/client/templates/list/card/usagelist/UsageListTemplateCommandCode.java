package ru.naumen.metainfoadmin.client.templates.list.card.usagelist;

import java.util.Set;

import com.google.common.collect.ImmutableSet;

/**
 * Команды для списка мест использования шаблона списка
 * <AUTHOR>
 * @since 08.08.2018
 */
public interface UsageListTemplateCommandCode
{
    String BREAK_LINK = "breakLinkListTemplate";
    String APPLY = "applyListTemplate";
    String APPLY_FOR_ALL = "applyForAllListTemplate";

    Set<String> COMMANDS_IN_LIST = ImmutableSet.of(BREAK_LINK, APPLY);
}