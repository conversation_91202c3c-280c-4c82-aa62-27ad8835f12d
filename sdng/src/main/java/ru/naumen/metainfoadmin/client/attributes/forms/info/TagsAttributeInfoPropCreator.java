package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.List;

import jakarta.inject.Inject;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.tags.data.TagServiceAsync;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;

/**
 * Создает {@link Property} для отображения информации о метках на модальной форме свойств атрибута.
 * <AUTHOR>
 * @since Feb 05, 2019
 */
public class TagsAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private TagPropertyFormatter tagPropertyFormatter;
    @Inject
    private TagServiceAsync tagServiceAsync;

    @Override
    protected void createInt(String code)
    {
        tagServiceAsync.getTags(attribute.getTags(),
                new BasicCallback<List<DtObject>>(rs)
                {
                    @Override
                    protected void handleSuccess(List<DtObject> value)
                    {
                        createProperty(code, tagPropertyFormatter.formatToAnchors(value).asString());
                    }
                });
    }
}
