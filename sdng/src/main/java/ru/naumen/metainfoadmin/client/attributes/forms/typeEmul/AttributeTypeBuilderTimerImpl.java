package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
@Singleton
public class AttributeTypeBuilderTimerImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(TimerAttributeType.DEFINITION,
                propertyValues.getProperty(AttributeFormPropertyCode.TARGET_TIMER));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        propertyValues.setProperty(AttributeFormPropertyCode.TARGET_TIMER,
                type.getProperty(TimerAttributeType.DEFINITION));

    }
}
