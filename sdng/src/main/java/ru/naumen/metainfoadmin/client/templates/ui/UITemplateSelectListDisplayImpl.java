package ru.naumen.metainfoadmin.client.templates.ui;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.GwtEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * Реализация виджета выбора шаблона.
 * <AUTHOR>
 * @since Jul 29, 2021
 */
public class UITemplateSelectListDisplayImpl implements UITemplateSelectListDisplay
{
    private final SingleSelectCellList<SelectItem> selectCellList;
    private final CommonMessages commonMessages;
    private final AdminWidgetResources widgetResources;

    private boolean enabled = true;

    @Inject
    public UITemplateSelectListDisplayImpl(Provider<SimpleSelectCellListBuilder<SelectItem>> selectListBuilderProvider,
            CommonMessages commonMessages, UITemplateMessages templateMessages, AdminWidgetResources widgetResources)
    {
        this.widgetResources = widgetResources;
        this.commonMessages = commonMessages;
        widgetResources.metainfoAdmin().ensureInjected();
        selectCellList = selectListBuilderProvider.get().setHasSearch(true).withEmptyOption().build();
        selectCellList.setHasSearchLite(true);
        selectCellList.customizeEmptyOption(commonMessages.brackets(templateMessages.newTemplate()));
        selectCellList.addStyleName(widgetResources.metainfoAdmin().templateSelectList());
        selectCellList.ensureDebugId("templateSelect");
        HTMLPanel changeStar = new HTMLPanel("*");
        changeStar.addStyleName(widgetResources.metainfoAdmin().templateChangeStar());
        selectCellList.getValueWidget().addToPanel(changeStar);
    }

    @Override
    public void addItem(DtObject dto, boolean highlight)
    {
        String title = StringUtilities.isEmpty(dto.getTitle()) ? commonMessages.untitled() : dto.getTitle();
        if (highlight)
        {
            selectCellList.addItemWithStyle(title, dto.getUUID(), widgetResources.all().fontBold());
        }
        else
        {
            selectCellList.addItem(title, dto.getUUID());
        }
        selectCellList.showValue();
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<String> handler)
    {
        return selectCellList.addValueChangeHandler(event -> handler.onValueChange(
                new ValueChangeEvent<String>(extractCode(event.getValue()))
                {
                }));
    }

    @Override
    public Widget asWidget()
    {
        return selectCellList;
    }

    @Override
    public void clear()
    {
        selectCellList.clear();
        selectCellList.showValue();
    }

    @Override
    public void fireEvent(GwtEvent<?> event)
    {
        selectCellList.fireEvent(event);
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
        selectCellList.setEnabled(enabled);
    }

    @Override
    public void setModified(boolean modified)
    {
        selectCellList.setStyleName(widgetResources.metainfoAdmin().templateChanged(), modified);
    }

    @Override
    public void setSelectedItemCode(@Nullable String code, boolean notify)
    {
        selectCellList.setObjValue(null == code ? null : new SelectItem(code, null), notify);
        selectCellList.showValue();
    }

    @Override
    public void startProcessing()
    {
        if (enabled)
        {
            selectCellList.setEnabled(false);
        }
    }

    @Override
    public void stopProcessing()
    {
        if (enabled)
        {
            selectCellList.setEnabled(true);
        }
    }

    @Nullable
    private static String extractCode(@Nullable SelectItem item)
    {
        return item == null ? null : item.getCode();
    }
}
