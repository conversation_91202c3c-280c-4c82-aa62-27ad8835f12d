package ru.naumen.metainfoadmin.client.templates.content.form;

import java.util.Collection;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.SingleSelectionModel;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassSameClassOnlyTreeContext;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.RoleUtils;
import ru.naumen.metainfo.shared.TitledClassFqn;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentFormType;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesPresenter;
import ru.naumen.metainfoadmin.client.sec.AdminSecMessages;

/**
 * Представление формы редактирования шаблона контента.
 * <AUTHOR>
 * @since Mar 25, 2021
 */
public class EditContentTemplateFormPresenter extends ContentTemplateFormBasePresenter
        implements CallbackPresenter<DtObject, DtObject>
{
    private DtObject contentTemplateDto;
    private Property<Collection<SelectItem>> profiles;
    private Property<DtObject> metaClassFqn;

    @Inject
    private ContentUtils contentUtils;
    @Inject
    private AdminSecMessages secMessages;

    @Inject
    private PopupValueCellTreeFactory<DtObject, DtObject, SingleSelectionModel<DtObject>> treeFactory;
    @Inject
    private DtoMetaClassesTreeFactory<SingleSelectionModel<DtObject>, DtoMetaClassSameClassOnlyTreeContext> treeModelHelper;

    @Inject
    public EditContentTemplateFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public ContentFormType getContentFormType()
    {
        return ContentFormType.EDIT_TEMPLATE;
    }

    @Override
    public void init(@Nullable DtObject value, AsyncCallback<DtObject> callback)
    {
        contentTemplateDto = value;
        init(callback);
    }

    @Override
    protected String getInitSettingsSetValue()
    {
        return contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.SETTINGS_SET);
    }

    @Override
    protected void bindCommonProperties()
    {
        super.bindCommonProperties();
        code.setDisable();
        code.setValue(contentTemplateDto.getUUID());
        title.setValue(contentTemplateDto.getTitle());

        metaClassFqn = createMetaClassSelectionTree();
        DebugIdBuilder.ensureDebugId(metaClassFqn, "classFqn");
        metaClassFqn.setValidationMarker(true);
        getDisplay().add(metaClassFqn);
        TitledClassFqn titledClassFqn = contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.CLASS_FQN);
        if (null != titledClassFqn)
        {
            metaClassFqn.setValue(new SimpleDtObject(titledClassFqn.asString(), titledClassFqn.getTitle(),
                    titledClassFqn));
        }

        ContentTemplate template = Objects.requireNonNull(contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.TEMPLATE));
        bindContentProperties(contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.CONTENT_TYPE));
        profiles = contentUtils.createProfilesProperty(null, template.getTemplate(), false,
                profile -> !RoleUtils.hasOnlyRelativeRole(profile.getRoles()));
        profiles.setDescription(secMessages.onlyWithAbsoluteRoles());
        DebugIdBuilder.ensureDebugId(profiles, "profiles");
        getDisplay().add(profiles);
    }

    @Override
    protected void fillTemplate(ContentTemplate template)
    {
        super.fillTemplate(template);
        contentPropertiesPresenter.setContentProperties(template.getTemplate());
        template.getTemplate().getProfiles().clear();
        template.getTemplate().getProfiles().addAll(SelectListPropertyValueExtractor.getCollectionValue(profiles));
        template.setClassFqn(metaClassFqn.getValue().getMetaClass());
    }

    @Override
    protected int getContentPropertiesIndex()
    {
        return getDisplay().indexOf(metaClassFqn) + 1;
    }

    @Override
    protected ContentTemplate getCurrentTemplate()
    {
        return Objects.requireNonNull(contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.TEMPLATE));
    }

    @Override
    protected String getFormTitle()
    {
        return messages.editTemplateForm();
    }

    @Override
    protected FlowContent getTemplateContent(ContentPropertiesPresenter<FlowContent> contentPropertiesPresenter)
    {
        return getCurrentTemplate().getTemplate();
    }

    @Override
    protected void saveTemplate(ContentTemplate template, AsyncCallback<DtObject> callback)
    {
        contentTemplateServiceAsync.saveContentTemplate(template, callback);
    }

    private Property<DtObject> createMetaClassSelectionTree()
    {
        return new PropertyBase<>(
                commonMessages.clazz(), treeFactory.create(treeModelHelper.createMetaClassTreeViewModel(Container
                .create(new DtoMetaClassSameClassOnlyTreeContext(AbstractBO.FQN, MetaClassFilters.isNotSystem())))));
    }
}
