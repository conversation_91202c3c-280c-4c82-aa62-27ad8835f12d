package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.core.client.menu.NavigationCommonGinModule.MENU_ICON_TYPE_TITLES;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;
import com.google.inject.name.Named;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;

/**
 * Делегат биндинга свойства "Маркер" элемента левого меню
 * <AUTHOR>
 * @since 27.06.2020
 */
@Singleton
public class LeftMenuItemPresentationBindDelegateImpl extends PropertyDelegateBindImpl<SelectItem, ListBoxProperty>
{
    @Inject
    @Named(MENU_ICON_TYPE_TITLES)
    private Map<MenuIconType, String> menuIconTypeTitles;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        SingleSelectCellList<?> selList = property.getValueWidget();
        for (MenuIconType pres : MenuIconType.values())
        {
            selList.addItem(menuIconTypeTitles.get(pres), pres.name());
        }
        super.bindProperty(context, property, callback);
    }
}