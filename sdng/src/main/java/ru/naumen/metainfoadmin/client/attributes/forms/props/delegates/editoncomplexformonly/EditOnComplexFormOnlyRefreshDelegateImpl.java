package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editoncomplexformonly;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_RELATION;

import java.util.Objects;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Делегат обновления свойства "Редактирование только через расширенную форму".
 * <AUTHOR>
 * @since Sep 20, 2020
 */
@Singleton
public class EditOnComplexFormOnlyRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean isComplexRelation = !Objects.equals(context.getPropertyValues().getProperty(COMPLEX_RELATION),
                Boolean.FALSE.toString());
        callback.onSuccess(isComplexRelation);
    }
}