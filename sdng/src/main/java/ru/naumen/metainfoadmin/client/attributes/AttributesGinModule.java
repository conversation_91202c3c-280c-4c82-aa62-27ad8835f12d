package ru.naumen.metainfoadmin.client.attributes;

import jakarta.inject.Singleton;

import ru.naumen.admin.client.attributes.ModalFormDetectorStub;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.ModalFormDetector;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.SelectAttrEmptyOptionStrategy;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.SelectAttrWithEmptyStrategy;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinjector;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormsGinModule;

import com.google.gwt.inject.client.AbstractGinModule;

/**
 * <AUTHOR>
 * @since 04.04.2012
 */
public class AttributesGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        install(new AttributeFormsGinModule());
        bind(AttributeListColumnGinjector.class).to(AttributesGinjector.class).in(Singleton.class);

        bind(SelectAttrEmptyOptionStrategy.class).to(SelectAttrWithEmptyStrategy.class);
        bind(ModalFormDetector.class).to(ModalFormDetectorStub.class).in(Singleton.class);
    }
}
