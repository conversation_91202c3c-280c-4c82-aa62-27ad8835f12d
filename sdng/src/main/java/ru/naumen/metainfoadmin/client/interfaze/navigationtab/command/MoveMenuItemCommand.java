package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;

/**
 * Абстрактная команда смещения элемента меню
 * <AUTHOR>
 * @since 27 сент. 2013 г.
 */
public abstract class MoveMenuItemCommand<M extends IMenuItem> extends BaseCommandImpl<M, NavigationSettings>
{
    @Inject
    private DispatchAsync dispatch;

    protected final int direction;

    /**
     * @param param
     * @param direction направление перемещения статуса. -1 к началу, 1 к концу.
     * @throws IllegalArgumentException если значение <b>direction</b> не равно -1 или 1.
     */
    protected MoveMenuItemCommand(NavigationSettingsMenuItemAbstractCommandParam param, int direction)
    {
        super(param);
        if (direction != -1 && direction != 1)
        {
            throw new IllegalArgumentException("Argument 'direction' must be -1 or 1");
        }
        this.direction = direction;
    }

    @Override
    public void execute(CommandParam<M, NavigationSettings> param)
    {
        NavigationSettingsMenuItemAbstractCommandParam p = (NavigationSettingsMenuItemAbstractCommandParam)param;
        IMenuItem item = (IMenuItem)p.getValue();

        if (item.getParent() == null)
        {
            List<IMenuItem> items = p.getMenuItems();
            int idx = items.indexOf(item);
            if (idx >= 0)
            {
                int idx2 = idx + direction;
                Collections.swap(items, idx, idx2);
            }
        }
        else
        {
            int idx = item.getParent().getChildren().indexOf(item);
            if (idx >= 0)
            {
                int idx2 = idx + direction;
                Collections.swap(item.getParent().getChildren(), idx, idx2);
            }
        }
        MoveNavigationMenuItemAction action = getAction();
        final Map<String, LinkedList<String>> menuItemPaths = p.getMenuItemPaths();
        action.setPathToMenuItem(menuItemPaths.get(item.getCode()));
        action.setDirection(direction);
        action.setMenuItemCode(item.getCode());
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(p.getCallbackSafe()));
    }

    protected abstract MoveNavigationMenuItemAction getAction();

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof IMenuItem) || null == param)
        {
            return false;
        }
        IMenuItem item = (IMenuItem)input;
        NavigationSettingsMenuItemAbstractCommandParam p = (NavigationSettingsMenuItemAbstractCommandParam)getParam();
        List<IMenuItem> siblings = p.getSiblings(item);
        int idx = siblings.indexOf(item);
        return idx >= 0 && isPossible(idx, siblings.size());
    }

    protected abstract boolean isPossible(int index, int count);
}