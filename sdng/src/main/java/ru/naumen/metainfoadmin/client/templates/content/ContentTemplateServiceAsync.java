package ru.naumen.metainfoadmin.client.templates.content;

import java.util.Collection;
import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;

/**
 * Сервис для работы с шаблонами контентов.
 * <AUTHOR>
 * @since Mar 24, 2021
 */
public interface ContentTemplateServiceAsync
{
    /**
     * Создает новый шаблон контента на сервере.
     * @param template шаблон контента
     * @param callback функция, вызываемая после выполнения действия
     */
    void addContentTemplate(ContentTemplate template, AsyncCallback<DtObject> callback);

    /**
     * Создает новый шаблон контента на сервере.
     * @param title название шаблона
     * @param code код шаблона
     * @param properties свойства шаблона
     * @param callback функция, вызываемая после выполнения действия
     */
    void addContentTemplate(String title, String code, IProperties properties, AsyncCallback<DtObject> callback);

    /**
     * Удаляет указанные шаблоны контентов.
     * @param codes коды удаляемых шаблонов
     * @param callback функция, вызываемая после выполнения действия
     */
    void deleteContentTemplates(Collection<String> codes, AsyncCallback<Void> callback);

    /**
     * Загружает шаблон контента с сервера.
     * @param code код шаблона контента
     * @param callback функция, вызываемая после получения данных
     */
    void loadContentTemplate(String code, AsyncCallback<DtObject> callback);

    /**
     * Загружает список шаблонов в соответствии с переданными свойствами.
     * @param properties свойства окружения, от которых зависит список шаблонов
     * @param callback функция, вызываемая после получения данных
     */
    void loadContentTemplates(IProperties properties, AsyncCallback<List<DtObject>> callback);

    /**
     * Сохраняет новый шаблон контента на сервере.
     * @param template шаблон контента
     * @param callback функция, вызываемая после выполнения действия
     */
    void saveContentTemplate(ContentTemplate template, AsyncCallback<DtObject> callback);
}
