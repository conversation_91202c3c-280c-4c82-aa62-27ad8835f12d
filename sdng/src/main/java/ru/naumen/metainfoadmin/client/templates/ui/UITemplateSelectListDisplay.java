package ru.naumen.metainfoadmin.client.templates.ui;

import jakarta.annotation.Nullable;

import com.google.gwt.event.logical.shared.HasValueChangeHandlers;
import com.google.gwt.user.client.ui.HasEnabled;

import ru.naumen.core.client.content.toolbar.display.ToolDisplay;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Виджет выбора шаблона.
 * Встраивается в тулбар редактирования карточки метакласса
 *
 * <AUTHOR>
 * @since Jul 29, 2021
 */
public interface UITemplateSelectListDisplay extends ToolDisplay, HasValueChangeHandlers<String>, HasEnabled
{
    void addItem(DtObject dto, boolean highlight);

    void clear();

    void setModified(boolean modified);

    void setSelectedItemCode(@Nullable String code, boolean notify);
}
