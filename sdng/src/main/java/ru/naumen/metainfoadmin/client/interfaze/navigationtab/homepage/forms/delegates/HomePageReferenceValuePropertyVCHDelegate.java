package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfo.shared.Constants.HomePage;

/**
 * Делегат обновления значения свойства
 * {@link ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.ReferenceCode#REFERENCE_VALUE "Вкладка карточки"}
 *
 * <AUTHOR>
 * @since 15.01.2023
 */
public class HomePageReferenceValuePropertyVCHDelegate implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.setProperty(HomePage.REFERENCE_TAB_VALUE, null);
        context.getPropertyControllers().get(HomePage.REFERENCE_TAB_VALUE).refresh();
        if (context.getPropertyControllers().containsKey(HomePage.REFERENCE_UI_TEMPLATE))
        {
            context.setProperty(HomePage.REFERENCE_UI_TEMPLATE, null);
            context.getPropertyControllers().get(HomePage.REFERENCE_UI_TEMPLATE).refresh();
        }
    }
}