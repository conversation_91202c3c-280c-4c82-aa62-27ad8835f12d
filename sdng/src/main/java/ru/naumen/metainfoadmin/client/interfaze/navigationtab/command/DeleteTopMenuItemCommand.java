package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.dispatch.DeleteNavigationMenuItemAction;

/**
 * Команда удаления элемента верхнего меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class DeleteTopMenuItemCommand extends DeleteMenuItemCommand<MenuItem>
{
    public static final String ID = "deleteTopMenuItemCommand";

    @Inject
    public DeleteTopMenuItemCommand(@Assisted NavigationSettingsTMCommandParam param)
    {
        super(param);
    }

    @Override
    protected DeleteNavigationMenuItemAction getAction()
    {
        return new DeleteNavigationMenuItemAction();
    }
}