package ru.naumen.metainfoadmin.client.scheduler.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.scheduler.forms.TriggerFormPresenter;

/**
 * Команда добавления правила выполнения задачи планировщика.
 * <AUTHOR>
 */
public class AddTriggerCommand extends TriggerPresenterCommandBase
{
    @Inject
    protected SchedulerGinjector injector;

    @Inject
    public AddTriggerCommand(@Assisted TriggerCommandParam param)
    {
        super(param);
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected TriggerFormPresenter getTriggerFormPresenter()
    {
        return injector.addTriggerFormPresenter();
    }
}
