package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.required;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.HIDDEN_WHEN_EMPTY;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.REQUIRED;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.REQUIRED_IN_INTERFACE;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Делегат изменения значения свойства "Обязательный" - делает неактивным свойство "Обязательный в интерфейсе"
 * <AUTHOR>
 * @since 11.04.2016
 *
 */
public class RequiredVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean required = context.getPropertyValues().getProperty(REQUIRED);
        if (required)
        {
            context.setProperty(REQUIRED_IN_INTERFACE, false);
        }
        context.setPropertyEnabled(REQUIRED_IN_INTERFACE, !required);

        if (context.getPropertyControllers().get(REQUIRED_IN_INTERFACE) != null)
        {
            context.getRefreshProcess().startCustomProcess(Arrays.asList(REQUIRED_IN_INTERFACE));
            context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        }

        if (required)
        {
            context.setProperty(HIDDEN_WHEN_EMPTY, false);
        }

        if (context.getPropertyControllers().get(HIDDEN_WHEN_EMPTY) != null)
        {
            context.getRefreshProcess().startCustomProcess(Arrays.asList(HIDDEN_WHEN_EMPTY));
            context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        }
    }
}
