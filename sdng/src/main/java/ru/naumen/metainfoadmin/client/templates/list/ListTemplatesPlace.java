package ru.naumen.metainfoadmin.client.templates.list;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.client.activity.AbstractTreePlace;

/**
 * <AUTHOR>
 * @since 06.04.2018
 */
public class ListTemplatesPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ListTemplatesPlace>
    {

        @Override
        public ListTemplatesPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(ListTemplatesPlace place)
        {
            return "";
        }
    }

    public static final String PLACE_PREFIX = "listTemplates";

    public static final ListTemplatesPlace INSTANCE = new ListTemplatesPlace();

    public ListTemplatesPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "ListTemplatesPlace";
    }
}