package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Делегат для изменения значения свойства "ограничение количества знаков после запятой"
 * @param <F>
 *
 * <AUTHOR>
 * @since 10.07.2019
 */
public class DigitsCountRestrictionVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(Arrays.asList(DEFAULT_VALUE));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
