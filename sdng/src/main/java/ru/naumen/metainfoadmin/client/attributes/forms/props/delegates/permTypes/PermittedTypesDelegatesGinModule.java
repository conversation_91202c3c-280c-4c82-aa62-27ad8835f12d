package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;

/**
 * <AUTHOR>
 * @since 28.05.2012
 */
public class PermittedTypesDelegatesGinModule extends AbstractGinModule
{
    public interface PermittedTypeRefreshDelegateCode
    {
        String BACK_LINK = "backLink";
        String DIRECT_LINK = "directLink";
    }

    @Override
    protected void configure()
    {
        bindAddAttributeForm();
        bindEditAttributeForm();

        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<AsyncCallback<BatchResult>>(){}, GetPermittedRelatedTypesCallback.class)
            .build(GetPermittedRelatedTypesCallbackFactory.class));
        //@formatter:on
    }

    private void bindAddAttributeForm()
    {
        //@formatter:off
        bind(new TypeLiteral<PermittedTypesRefreshDelegate<ObjectFormAdd>>(){})
            .to(new TypeLiteral<PermittedTypesRefreshDelegateSelectorImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<PermittedTypesRefreshDelegate<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(PermittedTypeRefreshDelegateCode.BACK_LINK))
            .to(new TypeLiteral<PermittedTypesRefreshDelegateBackLinkImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<PermittedTypesRefreshDelegate<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(PermittedTypeRefreshDelegateCode.DIRECT_LINK))
            .to(new TypeLiteral<PermittedTypesRefreshDelegateDirectLinkAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        //@formatter:on
    }

    private void bindEditAttributeForm()
    {
        //@formatter:off
        bind(new TypeLiteral<PermittedTypesRefreshDelegate<ObjectFormEdit>>(){})
            .to(new TypeLiteral<PermittedTypesRefreshDelegateSelectorImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<PermittedTypesRefreshDelegate<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(PermittedTypeRefreshDelegateCode.BACK_LINK))
            .to(new TypeLiteral<PermittedTypesRefreshDelegateBackLinkImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<PermittedTypesRefreshDelegate<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(PermittedTypeRefreshDelegateCode.DIRECT_LINK))
            .to(PermittedTypesRefreshDelegateDirectLinkEditImpl.class)
            .in(Singleton.class);
        //@formatter:on
    }
}
