package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import jakarta.inject.Singleton;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.ValueCellListProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.DefaultValuePropertyControllerFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.DefaultValuePropertyControllerImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.aggrClasses.AggregateClassesBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.aggrClasses.AggregateClassesVCHDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attrType.AttrTypeBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attrType.AttrTypeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.AttrChainRefreshDelegateAdd;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.AttrChainRefreshDelegateEdit;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.AttrChainVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.AttrChainViewRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.RelatedObjectAttributeRefreshDelegateAdd;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.RelatedObjectAttributeRefreshDelegateEdit;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.RelatedObjectAttributeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.RelatedObjectHierarchyLevelRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.RelatedObjectMetaclassRefreshDelegateAdd;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject.RelatedObjectMetaclassRefreshDelegateEdit;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.FilterByScriptRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.FilterByScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.FilterByScriptVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.ScriptForFiltrationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAggrAttrGroupRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAggrAttrGroupRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAttrGroupRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform.ComputableOnFormEditRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform.ComputableOnFormRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform.ComputableOnFormScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform.ComputableOnFormVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable.ComputableRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable.ComputableRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable.ComputableVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AttributeDateTimeRestrictionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AttributeDateTimeRestrictionRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AvailableRestrictionTypesProvider;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AvailableRestrictionTypesProviderImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeCommonRestrictionRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeCommonRestrictionsBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionConditionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionConditionRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionScriptPropertyBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionTypeBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionTypeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionTypeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultByScriptRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultByScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultByScriptVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultValueLabelRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.ScriptForDefaultRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate.CompositeRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate.CompositeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate.CompositeVCHDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate.CompositeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine.DeterminableRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine.DeterminableVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine.DeterminerBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine.DeterminerRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editable.EditableInListsRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editable.EditableVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editoncomplexformonly.EditOnComplexFormOnlyRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP.ExportNDAPBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP.ExportNDAPRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP.ExportNDAPVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP.RelatedAttributeToExportRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.fts.AdvlistSemanticFilteringRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.GenRuleBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.GenerationRuleRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.GenerationRuleRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.GenerationRuleVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.UseGenerationRuleBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.UseGenerationRuleRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.UseGenerationRuleRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.UseGenerationRuleVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hasgroupseparators.HasGroupSeparatorRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hasgroupseparators.HasGroupSeparatorVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hiddenwhenempty.HiddenWhenEmptyRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hiddenwhennovalues.HiddenWhenNoPossibleValuesRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidearchived.HideArchivedRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidecaption.HideAttrCaptionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidecaption.HideAttrCaptionRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inherit.InheritBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inherit.InheritRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inherit.InheritVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeBindDeletageImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesDelegatesGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.edit.EditPresentationRefreshDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.edit.EditPresentationRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.edit.EditPresentationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show.ShowPresentationRefreshDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show.ShowPresentationRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show.ShowPresentationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.quickform.QuickAddFormRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.quickform.QuickEditFormRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.required.RequiredRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.required.RequiredVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.requiredInInterface.RequiredInInterfaceRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.script.ScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sets.SettingsSetAttributeFormBindDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sets.SettingsSetAttributeFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sort.SelectSortRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.tags.TagsBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.tags.TagsRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.catalog.TargetCatalogRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.catalog.TargetCatalogVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas.TargetClassRefreshDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas.TargetClassRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas.TargetClassVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink.DirectLinkRefreshDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink.DirectLinkRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink.DirectLinkVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer.TargetTimerRefreshDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer.TargetTimerRefreshDelegateEditImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.template.TemplateBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.template.TemplateRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class AttributeFormPropertyDelegatesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new PermittedTypesDelegatesGinModule());
        bindAddFormDelegates();
        bindEditFormDelegates();

        bindDefaultPropertyControllerFactory(ObjectFormAdd.class);
        bindDefaultPropertyControllerFactory(ObjectFormEdit.class);
    }

    private void bindAddFormDelegates()
    {
        //@formatter:off
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(EDITABLE))
            .to(new TypeLiteral<EditableVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION))
            .to(new TypeLiteral<ComplexRelationVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(ATTR_TYPE))
            .to(new TypeLiteral<AttrTypeVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(TARGET_CLASS))
            .to(new TypeLiteral<TargetClassVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(TARGET_CATALOG))
            .to(new TypeLiteral<TargetCatalogVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(AGGREGATE_CLASSES))
            .to(new TypeLiteral<AggregateClassesVCHDelegate<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(SHOW_PRS))
            .to(new TypeLiteral<ShowPresentationVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(EDIT_PRS))
            .to(new TypeLiteral<EditPresentationVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(SUGGEST_CATALOG))
            .to(new TypeLiteral<RefreshDefaultValueVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(SELECT_SORTING))
            .to(new TypeLiteral<RefreshDefaultValueVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(DIRECT_LINK_TARGET))
            .to(new TypeLiteral<DirectLinkVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(INHERIT))
            .to(new TypeLiteral<InheritVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(COMPUTABLE))
            .to(new TypeLiteral<ComputableVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(new TypeLiteral<UseGenerationRuleVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(GEN_RULE))
            .to(new TypeLiteral<GenerationRuleVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(DETERMINABLE))
            .to(new TypeLiteral<DeterminableVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(REQUIRED))
            .to(new TypeLiteral<RequiredVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(FILTERED_BY_SCRIPT))
            .to(new TypeLiteral<FilterByScriptVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(DEFAULT_BY_SCRIPT))
            .to(new TypeLiteral<DefaultByScriptVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(PERMITTED_TYPES))
            .to(new TypeLiteral<PermittedTypesVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(EXPORT_NDAP))
            .to(new TypeLiteral<ExportNDAPVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(INTERVAL_AVAILABLE_UNITS))
            .to(new TypeLiteral<IntervalAvailableUnitsVCHDelegate<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(NEED_STORE_UNITS))
            .to(new TypeLiteral<NeedStoreUnitsVCHDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(ATTR_CHAIN))
            .to(new TypeLiteral<AttrChainVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(RELATED_OBJECT_ATTRIBUTE))
            .to(new TypeLiteral<RelatedObjectAttributeVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_WHEN_NO_POSSIBLE_VALUES))
            .to(new TypeLiteral<HiddenWhenNoPossibleValuesRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
                .annotatedWith(Names.named(EDIT_ON_COMPLEX_FORM_ONLY))
                .to(new TypeLiteral<EditOnComplexFormOnlyRefreshDelegateImpl<ObjectFormAdd>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
                .annotatedWith(Names.named(HIDE_ARCHIVED))
                .to(new TypeLiteral<HideArchivedRefreshDelegateImpl<ObjectFormAdd>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(HAS_GROUP_SEPARATORS))
            .to(new TypeLiteral<HasGroupSeparatorVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);

        bind(new TypeLiteral<InputmaskVCHDelegateImpl<ObjectFormAdd>>(){}).in(Singleton.class);
        bind(new TypeLiteral<InputmaskModeVCHDelegateImpl<ObjectFormAdd>>(){}).in(Singleton.class);


        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(ATTR_TYPE))
            .to(new TypeLiteral<AttrTypeBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Collection<MetaClassLite>, ValueCellListProperty<MetaClassLite>>>(){})
            .annotatedWith(Names.named(AGGREGATE_CLASSES))
            .to(new TypeLiteral<AggregateClassesBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(INHERIT))
            .to(new TypeLiteral<InheritBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, SelectItem, ListBoxWithEmptyOptProperty>>(){})
            .annotatedWith(Names.named(DETERMINER))
            .to(new TypeLiteral<DeterminerBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM_SCRIPT))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_DEFAULT))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_FILTRATION))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_SCRIPT))
            .to(new TypeLiteral<DateTimeRestrictionScriptPropertyBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_SCRIPT))
            .to(new TypeLiteral<DateTimeRestrictionScriptRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(new TypeLiteral<DateTimeRestrictionTypeVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(new TypeLiteral<DateTimeRestrictionTypeBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);

        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_ATTR_CAPTION))
            .to(new TypeLiteral<HideAttrCaptionBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
         bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_ATTR_CAPTION))
            .to(new TypeLiteral<HideAttrCaptionRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_ATTR_CAPTION))
            .to(new TypeLiteral<HideAttrCaptionBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_ATTR_CAPTION))
            .to(new TypeLiteral<HideAttrCaptionRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);

        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Collection<SelectItem>, MultiSelectBoxProperty>>(){})
            .annotatedWith(Names.named(INTERVAL_AVAILABLE_UNITS))
            .to(new TypeLiteral<IntervalAvailableUnitsBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Boolean, BooleanCheckBoxInfoProperty>>(){})
            .annotatedWith(Names.named(NEED_STORE_UNITS))
            .to(new TypeLiteral<NeedStoreUnitsBindDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<InputmaskModeBindDeletageImpl<ObjectFormAdd>>(){}).in(Singleton.class);
        bind(new TypeLiteral<InputmaskBindDelegateImpl<ObjectFormAdd>>(){}).in(Singleton.class);

        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(INHERIT))
            .to(new TypeLiteral<InheritRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(TARGET_CATALOG))
            .to(new TypeLiteral<TargetCatalogRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(TARGET_CLASS))
            .to(new TypeLiteral<TargetClassRefreshDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(TARGET_TIMER))
            .to(new TypeLiteral<TargetTimerRefreshDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(SHOW_PRS))
            .to(new TypeLiteral<ShowPresentationRefreshDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(EDIT_PRS))
            .to(new TypeLiteral<EditPresentationRefreshDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(SUGGEST_CATALOG))
            .to(new TypeLiteral<SuggestCatalogRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(SELECT_SORTING))
            .to(new TypeLiteral<SelectSortRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DIRECT_LINK_TARGET))
            .to(new TypeLiteral<DirectLinkRefreshDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE))
            .to(new TypeLiteral<ComputableRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(new TypeLiteral<UseGenerationRuleRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(GEN_RULE))
            .to(new TypeLiteral<GenerationRuleRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(new TypeLiteral<UseGenerationRuleBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(GEN_RULE))
            .to(new TypeLiteral<GenRuleBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(TEMPLATE))
            .to(new TypeLiteral<TemplateBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(DETERMINABLE))
            .to(new TypeLiteral<DeterminableRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxWithEmptyOptProperty>>(){})
            .annotatedWith(Names.named(DETERMINER))
            .to(new TypeLiteral<DeterminerRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(FILTERED_BY_SCRIPT))
            .to(new TypeLiteral<FilterByScriptRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(DEFAULT_BY_SCRIPT))
            .to(new TypeLiteral<DefaultByScriptRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT))
            .to(new TypeLiteral<ScriptRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>>(){})
            .annotatedWith(Names.named(DATE_TIME_COMMON_RESTRICTIONS))
            .to(new TypeLiteral<DateTimeCommonRestrictionsBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>>(){})
            .annotatedWith(Names.named(DATE_TIME_COMMON_RESTRICTIONS))
            .to(new TypeLiteral<DateTimeCommonRestrictionRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(new TypeLiteral<DateTimeRestrictionTypeRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_ATTRIBUTE))
            .to(new TypeLiteral<AttributeDateTimeRestrictionRefreshDelegate<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_ATTRIBUTE))
            .to(new TypeLiteral<AttributeDateTimeRestrictionBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_CONDITION))
            .to(new TypeLiteral<DateTimeRestrictionConditionRefreshDelegate<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_CONDITION))
            .to(new TypeLiteral<DateTimeRestrictionConditionBindDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);

        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_FILTRATION))
            .to(new TypeLiteral<ScriptForFiltrationRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_DEFAULT))
            .to(new TypeLiteral<ScriptForDefaultRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(EDITABLE))
            .to(new TypeLiteral<BooleanComputableDependentRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Collection<SelectItem>, MultiSelectBoxProperty>>(){})
            .annotatedWith(Names.named(INTERVAL_AVAILABLE_UNITS))
            .to(new TypeLiteral<IntervalAvailableUnitsRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxInfoProperty>>(){})
            .annotatedWith(Names.named(NEED_STORE_UNITS))
            .to(new TypeLiteral<NeedStoreUnitsRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION))
            .to(new TypeLiteral<ComplexRelationRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION_ATTR_GROUP))
            .to(new TypeLiteral<ComplexRelationAttrGroupRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW))
            .to(new TypeLiteral<ComplexRelationStructuredObjectsViewRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxWithEmptyOptProperty>>(){},
                        new TypeLiteral<ComplexRelationAggrAttrGroupRefreshDelegateImpl<ObjectFormAdd>>(){})
                .build(new TypeLiteral<ComplexRelationAggrAttrGroupRefreshDelegateFactory<ObjectFormAdd>>(){}));

        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(EDITABLE_IN_LISTS))
            .to(new TypeLiteral<EditableInListsRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(REQUIRED))
            .to(new TypeLiteral<RequiredRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(REQUIRED_IN_INTERFACE))
            .to(new TypeLiteral<RequiredInInterfaceRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HAS_GROUP_SEPARATORS))
            .to(new TypeLiteral<HasGroupSeparatorRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(UNIQUE))
            .to(new TypeLiteral<BooleanComputableDependentRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, String, LabelProperty>>(){})
            .annotatedWith(Names.named(DEFAULT_VALUE_LABEL))
            .to(new TypeLiteral<DefaultValueLabelRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(COMPOSITE))
            .to(new TypeLiteral<CompositeRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(COMPOSITE))
            .to(new TypeLiteral<CompositeVCHDelegateAddImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(TEMPLATE))
            .to(new TypeLiteral<TemplateRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM))
            .to(new TypeLiteral<ComputableOnFormRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormAdd>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM))
            .to(new TypeLiteral<ComputableOnFormVCHDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM_SCRIPT))
            .to(new TypeLiteral<ComputableOnFormScriptRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Boolean, BooleanCheckBoxInfoProperty>>(){})
                .annotatedWith(Names.named(EXPORT_NDAP))
                .to(new TypeLiteral<ExportNDAPBindDelegateImpl<ObjectFormAdd>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxInfoProperty>>(){})
            .annotatedWith(Names.named(EXPORT_NDAP))
            .to(new TypeLiteral<ExportNDAPRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Collection<SelectItem>, MultiSelectBoxProperty>>(){})
            .annotatedWith(Names.named(RELATED_ATTRS_TO_EXPORT))
            .to(new TypeLiteral<RelatedAttributeToExportRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_WHEN_EMPTY))
            .to(new TypeLiteral<HiddenWhenEmptyRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(QUICK_ADD_FORM_CODE))
            .to(new TypeLiteral<QuickAddFormRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(QUICK_EDIT_FORM_CODE))
            .to(new TypeLiteral<QuickEditFormRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, RelationsAttrTreeObject,
             PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject, FilteredSingleSelectionModel<RelationsAttrTreeObject>>>>>(){})
            .annotatedWith(Names.named(ATTR_CHAIN))
            .to(new TypeLiteral<AttrChainRefreshDelegateAdd<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, String, TextBoxProperty>>(){})
            .annotatedWith(Names.named(ATTR_CHAIN_VIEW))
            .to(new TypeLiteral<AttrChainViewRefreshDelegate<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(RELATED_OBJECT_ATTRIBUTE))
            .to(new TypeLiteral<RelatedObjectAttributeRefreshDelegateAdd<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, String, TextBoxProperty>>(){})
             .annotatedWith(Names.named(RELATED_OBJECT_METACLASS))
             .to(new TypeLiteral<RelatedObjectMetaclassRefreshDelegateAdd<ObjectFormAdd>>(){})
             .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
             .annotatedWith(Names.named(RELATED_OBJECT_HIERARCHY_LEVEL))
             .to(new TypeLiteral<RelatedObjectHierarchyLevelRefreshDelegateImpl<ObjectFormAdd>>(){})
             .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, Collection<SelectItem>, TagsProperty>>(){})
             .annotatedWith(Names.named(TAGS))
             .to(new TypeLiteral<TagsBindDelegateImpl<ObjectFormAdd>>(){})
             .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormAdd, SelectItem, ListBoxWithEmptyOptProperty>>(){})
                .annotatedWith(Names.named(SETTINGS_SET))
                .to(new TypeLiteral<SettingsSetAttributeFormBindDelegate<ObjectFormAdd>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Collection<SelectItem>, TagsProperty>>(){})
             .annotatedWith(Names.named(TAGS))
             .to(new TypeLiteral<TagsRefreshDelegateImpl<ObjectFormAdd>>(){})
             .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxWithEmptyOptProperty>>(){})
                .annotatedWith(Names.named(SETTINGS_SET))
                .to(new TypeLiteral<SettingsSetAttributeFormRefreshDelegate<ObjectFormAdd>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<InputmaskRefreshDelegateImpl<ObjectFormAdd>>(){}).in(Singleton.class);
        bind(new TypeLiteral<InputmaskModeRefreshDelegateImpl<ObjectFormAdd>>(){}).in(Singleton.class);
        bind(new TypeLiteral<AvailableRestrictionTypesProvider<ObjectFormAdd>>(){})
            .to(new TypeLiteral<AvailableRestrictionTypesProviderImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, Boolean, BooleanCheckBoxProperty>>(){})
                .annotatedWith(Names.named(ADVLIST_SEMANTIC_FILTERING))
                .to(new TypeLiteral<AdvlistSemanticFilteringRefreshDelegateImpl<ObjectFormAdd>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormAdd, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE))
            .to(new TypeLiteral<StructuredObjectsViewForBuildingTreeRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        //@formatter:on

    }

    private void bindDefaultPropertyControllerFactory(Class<? extends ObjectForm> form)
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, DefaultValuePropertyControllerImpl.class)
                .build(Gin.parameterizedTypeLiteral(DefaultValuePropertyControllerFactory.class, form)));
        //@formatter:on
    }

    private void bindEditFormDelegates()
    {
        //@formatter:off
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(EDITABLE))
            .to(new TypeLiteral<EditableVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION))
            .to(new TypeLiteral<ComplexRelationVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(ATTR_TYPE))
            .to(new TypeLiteral<AttrTypeVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(TARGET_CLASS))
            .to(new TypeLiteral<TargetClassVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(TARGET_CATALOG))
            .to(new TypeLiteral<TargetCatalogVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(AGGREGATE_CLASSES))
            .to(new TypeLiteral<AggregateClassesVCHDelegate<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(SHOW_PRS))
            .to(new TypeLiteral<ShowPresentationVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(EDIT_PRS))
            .to(new TypeLiteral<EditPresentationVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(SUGGEST_CATALOG))
            .to(new TypeLiteral<RefreshDefaultValueVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(SELECT_SORTING))
            .to(new TypeLiteral<RefreshDefaultValueVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(DIRECT_LINK_TARGET))
            .to(new TypeLiteral<DirectLinkVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(INHERIT))
            .to(new TypeLiteral<InheritVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(COMPUTABLE))
            .to(new TypeLiteral<ComputableVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(new TypeLiteral<UseGenerationRuleVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(new TypeLiteral<UseGenerationRuleBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(GEN_RULE))
            .to(new TypeLiteral<GenerationRuleVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(GEN_RULE))
            .to(new TypeLiteral<GenRuleBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(TEMPLATE))
            .to(new TypeLiteral<TemplateBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(DETERMINABLE))
            .to(new TypeLiteral<DeterminableVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(REQUIRED))
            .to(new TypeLiteral<RequiredVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(FILTERED_BY_SCRIPT))
            .to(new TypeLiteral<FilterByScriptVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(DEFAULT_BY_SCRIPT))
            .to(new TypeLiteral<DefaultByScriptVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(PERMITTED_TYPES))
            .to(new TypeLiteral<PermittedTypesVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(EXPORT_NDAP))
            .to(new TypeLiteral<ExportNDAPVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(INTERVAL_AVAILABLE_UNITS))
            .to(new TypeLiteral<IntervalAvailableUnitsVCHDelegate<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(NEED_STORE_UNITS))
            .to(new TypeLiteral<NeedStoreUnitsVCHDelegateEditImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(HAS_GROUP_SEPARATORS))
            .to(new TypeLiteral<HasGroupSeparatorVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(ATTR_CHAIN))
            .to(new TypeLiteral<AttrChainVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(RELATED_OBJECT_ATTRIBUTE))
            .to(new TypeLiteral<RelatedObjectAttributeVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_WHEN_NO_POSSIBLE_VALUES))
            .to(new TypeLiteral<HiddenWhenNoPossibleValuesRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
                .annotatedWith(Names.named(EDIT_ON_COMPLEX_FORM_ONLY))
                .to(new TypeLiteral<EditOnComplexFormOnlyRefreshDelegateImpl<ObjectFormEdit>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<InputmaskVCHDelegateImpl<ObjectFormEdit>>(){}).in(Singleton.class);
        bind(new TypeLiteral<InputmaskModeVCHDelegateImpl<ObjectFormEdit>>(){}).in(Singleton.class);


        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(ATTR_TYPE))
            .to(new TypeLiteral<AttrTypeBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Collection<MetaClassLite>, ValueCellListProperty<MetaClassLite>>>(){})
            .annotatedWith(Names.named(AGGREGATE_CLASSES))
            .to(new TypeLiteral<AggregateClassesBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(INHERIT))
            .to(new TypeLiteral<InheritBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Collection<SelectItem>, MultiSelectBoxProperty>>(){})
            .annotatedWith(Names.named(INTERVAL_AVAILABLE_UNITS))
            .to(new TypeLiteral<IntervalAvailableUnitsBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Boolean, BooleanCheckBoxInfoProperty>>(){})
            .annotatedWith(Names.named(NEED_STORE_UNITS))
            .to(new TypeLiteral<NeedStoreUnitsBindDelegateEditImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, SelectItem, ListBoxWithEmptyOptProperty>>(){})
            .annotatedWith(Names.named(DETERMINER))
            .to(new TypeLiteral<DeterminerBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM_SCRIPT))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_DEFAULT))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_FILTRATION))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT))
            .to(new TypeLiteral<ScriptPropertyBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<InputmaskModeBindDeletageImpl<ObjectFormEdit>>(){}).in(Singleton.class);
        bind(new TypeLiteral<InputmaskBindDelegateImpl<ObjectFormEdit>>(){}).in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>>(){})
            .annotatedWith(Names.named(DATE_TIME_COMMON_RESTRICTIONS))
            .to(new TypeLiteral<DateTimeCommonRestrictionRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>>(){})
            .annotatedWith(Names.named(DATE_TIME_COMMON_RESTRICTIONS))
            .to(new TypeLiteral<DateTimeCommonRestrictionsBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_SCRIPT))
            .to(new TypeLiteral<DateTimeRestrictionScriptPropertyBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(new TypeLiteral<DateTimeRestrictionTypeVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(new TypeLiteral<DateTimeRestrictionTypeBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_CONDITION))
            .to(new TypeLiteral<DateTimeRestrictionConditionRefreshDelegate<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_CONDITION))
            .to(new TypeLiteral<DateTimeRestrictionConditionBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_ATTRIBUTE))
            .to(new TypeLiteral<AttributeDateTimeRestrictionRefreshDelegate<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_ATTRIBUTE))
            .to(new TypeLiteral<AttributeDateTimeRestrictionBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);

        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(INHERIT))
            .to(new TypeLiteral<InheritRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(TARGET_CATALOG))
            .to(new TypeLiteral<TargetCatalogRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(TARGET_CLASS))
            .to(TargetClassRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(TARGET_TIMER))
            .to(TargetTimerRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(SHOW_PRS))
            .to(ShowPresentationRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(EDIT_PRS))
            .to(EditPresentationRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(SUGGEST_CATALOG))
            .to(new TypeLiteral<SuggestCatalogRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(SELECT_SORTING))
            .to(new TypeLiteral<SelectSortRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DIRECT_LINK_TARGET))
            .to(DirectLinkRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE))
            .to(ComputableRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(UseGenerationRuleRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(GEN_RULE))
            .to(GenerationRuleRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(DETERMINABLE))
            .to(new TypeLiteral<DeterminableRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxWithEmptyOptProperty>>(){})
            .annotatedWith(Names.named(DETERMINER))
            .to(new TypeLiteral<DeterminerRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(FILTERED_BY_SCRIPT))
            .to(FilterByScriptRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(DEFAULT_BY_SCRIPT))
            .to(DefaultByScriptRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT))
            .to(new TypeLiteral<ScriptRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_FILTRATION))
            .to(new TypeLiteral<ScriptForFiltrationRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(SCRIPT_FOR_DEFAULT))
            .to(new TypeLiteral<ScriptForDefaultRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(EDITABLE))
            .to(new TypeLiteral<BooleanComputableDependentRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxInfoProperty>>(){})
             .annotatedWith(Names.named(NEED_STORE_UNITS))
             .to(new TypeLiteral<NeedStoreUnitsRefreshDelegateImpl<ObjectFormEdit>>(){})
             .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Collection<SelectItem>, MultiSelectBoxProperty>>(){})
            .annotatedWith(Names.named(INTERVAL_AVAILABLE_UNITS))
            .to(new TypeLiteral<IntervalAvailableUnitsRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION))
            .to(new TypeLiteral<ComplexRelationRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION_ATTR_GROUP))
            .to(new TypeLiteral<ComplexRelationAttrGroupRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW))
            .to(new TypeLiteral<ComplexRelationStructuredObjectsViewRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_SCRIPT))
            .to(new TypeLiteral<DateTimeRestrictionScriptRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(new TypeLiteral<DateTimeRestrictionTypeRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);

        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxWithEmptyOptProperty>>(){},
                    new TypeLiteral<ComplexRelationAggrAttrGroupRefreshDelegateImpl<ObjectFormEdit>>(){})
            .build(new TypeLiteral<ComplexRelationAggrAttrGroupRefreshDelegateFactory<ObjectFormEdit>>(){}));

        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(EDITABLE_IN_LISTS))
            .to(new TypeLiteral<EditableInListsRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(REQUIRED))
            .to(new TypeLiteral<RequiredRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(REQUIRED_IN_INTERFACE))
            .to(new TypeLiteral<RequiredInInterfaceRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HAS_GROUP_SEPARATORS))
            .to(new TypeLiteral<HasGroupSeparatorRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(UNIQUE))
            .to(new TypeLiteral<BooleanComputableDependentRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, String, LabelProperty>>(){})
            .annotatedWith(Names.named(DEFAULT_VALUE_LABEL))
            .to(new TypeLiteral<DefaultValueLabelRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(COMPOSITE))
            .to(CompositeRefreshDelegateEditImpl.class)
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(COMPOSITE))
            .to(new TypeLiteral<CompositeVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, String, FootedTextBoxProperty>>(){})
            .annotatedWith(Names.named(TEMPLATE))
            .to(new TypeLiteral<TemplateRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM))
            .to(new TypeLiteral<ComputableOnFormEditRefreshDelegateImpl>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateVCH<ObjectFormEdit>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM))
            .to(new TypeLiteral<ComputableOnFormVCHDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, ScriptDto, ScriptComponentEditProperty>>(){})
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM_SCRIPT))
            .to(new TypeLiteral<ComputableOnFormScriptRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Boolean, BooleanCheckBoxInfoProperty>>(){})
                .annotatedWith(Names.named(EXPORT_NDAP))
                .to(new TypeLiteral<ExportNDAPBindDelegateImpl<ObjectFormEdit>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean,
                BooleanCheckBoxInfoProperty>>(){})
            .annotatedWith(Names.named(EXPORT_NDAP))
            .to(new TypeLiteral<ExportNDAPRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Collection<SelectItem>, MultiSelectBoxProperty>>(){})
            .annotatedWith(Names.named(RELATED_ATTRS_TO_EXPORT))
            .to(new TypeLiteral<RelatedAttributeToExportRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
            .annotatedWith(Names.named(HIDDEN_WHEN_EMPTY))
            .to(new TypeLiteral<HiddenWhenEmptyRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(QUICK_ADD_FORM_CODE))
            .to(new TypeLiteral<QuickAddFormRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(QUICK_EDIT_FORM_CODE))
            .to(new TypeLiteral<QuickEditFormRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, RelationsAttrTreeObject,
                PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject, FilteredSingleSelectionModel<RelationsAttrTreeObject>>>>>(){})
            .annotatedWith(Names.named(ATTR_CHAIN))
            .to(new TypeLiteral<AttrChainRefreshDelegateEdit<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, String, TextBoxProperty>>(){})
            .annotatedWith(Names.named(ATTR_CHAIN_VIEW))
            .to(new TypeLiteral<AttrChainViewRefreshDelegate<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(RELATED_OBJECT_ATTRIBUTE))
            .to(new TypeLiteral<RelatedObjectAttributeRefreshDelegateEdit>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, String, TextBoxProperty>>(){})
            .annotatedWith(Names.named(RELATED_OBJECT_METACLASS))
            .to(new TypeLiteral<RelatedObjectMetaclassRefreshDelegateEdit<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(RELATED_OBJECT_HIERARCHY_LEVEL))
            .to(new TypeLiteral<RelatedObjectHierarchyLevelRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, Collection<SelectItem>, TagsProperty>>(){})
            .annotatedWith(Names.named(TAGS))
            .to(new TypeLiteral<TagsBindDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateBind<ObjectFormEdit, SelectItem, ListBoxWithEmptyOptProperty>>(){})
                .annotatedWith(Names.named(SETTINGS_SET))
                .to(new TypeLiteral<SettingsSetAttributeFormBindDelegate<ObjectFormEdit>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Collection<SelectItem>, TagsProperty>>(){})
            .annotatedWith(Names.named(TAGS))
            .to(new TypeLiteral<TagsRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxWithEmptyOptProperty>>(){})
                .annotatedWith(Names.named(SETTINGS_SET))
                .to(new TypeLiteral<SettingsSetAttributeFormRefreshDelegate<ObjectFormEdit>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
                .annotatedWith(Names.named(ADVLIST_SEMANTIC_FILTERING))
                .to(new TypeLiteral<AdvlistSemanticFilteringRefreshDelegateImpl<ObjectFormEdit>>(){})
                .in(Singleton.class);

        bind(new TypeLiteral<InputmaskRefreshDelegateImpl<ObjectFormEdit>>(){}).in(Singleton.class);
        bind(new TypeLiteral<InputmaskModeRefreshDelegateImpl<ObjectFormEdit>>(){}).in(Singleton.class);
        bind(new TypeLiteral<AvailableRestrictionTypesProvider<ObjectFormEdit>>(){})
            .to(new TypeLiteral<AvailableRestrictionTypesProviderImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, Boolean, BooleanCheckBoxProperty>>(){})
                .annotatedWith(Names.named(HIDE_ARCHIVED))
                .to(new TypeLiteral<HideArchivedRefreshDelegateImpl<ObjectFormEdit>>(){})
                .in(Singleton.class);
        bind(new TypeLiteral<AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>>(){})
            .annotatedWith(Names.named(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE))
            .to(new TypeLiteral<StructuredObjectsViewForBuildingTreeRefreshDelegateImpl<ObjectFormEdit>>(){})
            .in(Singleton.class);
        //@formatter:on
    }
}
