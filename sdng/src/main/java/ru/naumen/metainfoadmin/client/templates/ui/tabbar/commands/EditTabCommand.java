package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.EditTabModelFormPresenter;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabModel;

/**
 * Команда редактирования шаблона вкладки.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class EditTabCommand extends BaseCommandImpl<TabModel, Void>
{
    private final List<TabModel> tabs;
    private final Provider<EditTabModelFormPresenter> editFormProvider;

    @Inject
    public EditTabCommand(@Assisted TabModelCommandParam param,
            Provider<EditTabModelFormPresenter> editFormProvider)
    {
        super(param);
        this.tabs = param.getTabs();
        this.editFormProvider = editFormProvider;
    }

    @Override
    public void execute(CommandParam<TabModel, Void> param)
    {
        EditTabModelFormPresenter formPresenter = editFormProvider.get();
        formPresenter.init(param.getValue(), new BasicCallback<TabModel>()
        {
            @Override
            protected void handleSuccess(TabModel model)
            {
                if (tabs.contains(param.getValue()))
                {
                    tabs.set(tabs.indexOf(param.getValue()), model);
                    param.getCallbackSafe().onSuccess(null);
                }
            }
        });
        formPresenter.bind();
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof TabModel && !((TabModel)input).getCode().isEmpty();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
