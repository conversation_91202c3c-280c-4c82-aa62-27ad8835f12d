package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.dispatch2.DirectLinkAttributeDescription;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class DirectLinkRefreshDelegateEditImpl extends DirectLinkRefreshDelegateImpl<ObjectFormEdit>
{
    private class EditRefreshCallback extends RefreshCallback
    {
        public EditRefreshCallback(AsyncCallback<Boolean> callback, ListBoxProperty property,
                PropertyContainerContext context)
        {
            super(callback, property, context);
        }

        @Override
        protected void getMetaClass(Collection<DirectLinkAttributeDescription> attrDescs)
        {
            Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
            String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
            String attrCode = attribute.getType().getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
            metainfoService.getMetaClass(ClassFqn.parse(fqnAsString), new GetMetaClassBaseCallback(context, attrCode)
            {
                @Override
                protected void handleSuccess(MetaClass metaClass)
                {
                    super.handleSuccess(metaClass);
                    callback.onSuccess(true);
                }
            });
        }

        @Override
        protected void setPropertyValue()
        {
            Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
            String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
            String attrCode = attribute.getType().getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
            context.setProperty(AttributeFormPropertyCode.DIRECT_LINK_TARGET, valueFormatter.to(fqnAsString, attrCode));
        }
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        MetaClass metaClass = context.getContextValues().<MetaClass> getProperty(AttributeFormContextValues.METAINFO);
        metainfoService.findDirectLinkAttributes(metaClass.getFqn(), new EditRefreshCallback(callback, property,
                context));
    }
}
