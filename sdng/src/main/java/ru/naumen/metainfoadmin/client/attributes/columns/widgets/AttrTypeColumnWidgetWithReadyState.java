package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import jakarta.inject.Inject;

import com.google.inject.name.Named;

import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule;

public abstract class AttrTypeColumnWidgetWithReadyState implements AttrTypeColumnWidget
{
    @Inject
    @Named(AttributeListColumnGinModule.ATTR_LIST_READY_STATE)
    protected ReadyState rs;
}
