package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inherit;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат, определяющий видимость свойства "Наследовать параметры"
 * <AUTHOR>
 * @since 25.05.2012
 */
public class InheritRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(context.getContextValues().<Boolean> getProperty(AttributeFormContextValues.HAS_INHERIT));
    }
}
