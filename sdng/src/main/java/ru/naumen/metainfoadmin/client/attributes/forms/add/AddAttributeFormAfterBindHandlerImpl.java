package ru.naumen.metainfoadmin.client.attributes.forms.add;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.AGGREGATE_CLASSES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;

import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormAfterBindHandlerImpl;

/**
 * Действия по окончании биндинга формы добавления атрибута
 * <AUTHOR>
 * @since 23.05.2012
 */
public class AddAttributeFormAfterBindHandlerImpl<T extends ObjectFormAdd> extends AttributeFormAfterBindHandlerImpl<T>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(ATTR_TYPE).getValue();
        context.getPropertyControllers().get(AGGREGATE_CLASSES).getValue();
        super.onAfterContainerBind(context);
        context.getEventBus().fireEvent(new UpdateTabOrderEvent(true));
    }
}
