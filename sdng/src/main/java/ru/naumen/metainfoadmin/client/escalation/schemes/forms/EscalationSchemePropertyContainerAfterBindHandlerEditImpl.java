package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;

/**
 * <AUTHOR>
 * @since 06.02.2017
 */
public class EscalationSchemePropertyContainerAfterBindHandlerEditImpl
        extends EscalationSchemePropertyContainerAfterBindHandlerImpl<ObjectFormEdit>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        super.onAfterContainerBind(context);
        context.setDisabled(Constants.Catalog.CODE);
        context.getDisplay().display();
    }
}
