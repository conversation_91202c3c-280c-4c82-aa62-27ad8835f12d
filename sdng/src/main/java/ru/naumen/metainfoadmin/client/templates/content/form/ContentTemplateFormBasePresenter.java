package ru.naumen.metainfoadmin.client.templates.content.form;

import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Provider;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.AbstractMessageWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesOwner;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesPresenterRegistry;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateMessages;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateServiceAsync;

/**
 * Представление формы добавления/редактирования шаблона контента.
 * <AUTHOR>
 * @since Mar 18, 2021
 */
public abstract class ContentTemplateFormBasePresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements ContentPropertiesOwner
{
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    private Property<SelectItem> settingsSet;
    @Inject
    protected ContentTemplateMessages messages;
    @Inject
    protected Processor validator;
    @Inject
    protected ContentPropertiesPresenterRegistry propertiesPresenterRegistry;
    @Inject
    protected ContentTemplateServiceAsync contentTemplateServiceAsync;
    @Inject
    protected CommonMessages commonMessages;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    protected ContentPropertiesPresenter<FlowContent> contentPropertiesPresenter;
    protected String contentType;

    @Inject
    private Provider<NotEmptyValidator> notEmptyValidatorProvider;
    @Inject
    private MetainfoUtils metainfoUtils;

    private AsyncCallback<DtObject> applyCallback;

    public ContentTemplateFormBasePresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public <P> PropertyRegistration<P> addPropertyAfter(Property<P> property, Property<?> propertyBefore)
    {
        int index = getDisplay().indexOf(propertyBefore) + 1;
        return getDisplay().addProperty(property, index);
    }

    @Nullable
    @Override
    public AbstractMessageWidget getAttention()
    {
        return getDisplay().getAttention();
    }

    @Nullable
    @Override
    public <P> PropertyRegistration<P> getPropertyRegistration(Property<P> property)
    {
        return getDisplay().getPropertyRegistration(property);
    }

    @Override
    public Processor getValidationProcessor()
    {
        return validator;
    }

    public void init(AsyncCallback<DtObject> applyCallback)
    {
        this.applyCallback = new BasicCallback<DtObject>()
        {
            @Override
            protected void handleSuccess(DtObject value)
            {
                applyCallback.onSuccess(value);
            }

            @Override
            protected void handleFailure(String msg, @Nullable String details)
            {
                getDisplay().addErrorMessage(msg);
            }
        };
    }

    @Override
    public void onApply()
    {
        if (!validator.validate())
        {
            return;
        }
        ContentTemplate template = getCurrentTemplate();
        fillTemplate(template);
        saveTemplate(template, new CallbackDecorator<DtObject, DtObject>(applyCallback)
        {
            @Override
            protected DtObject apply(DtObject from)
            {
                afterSave(from);
                return from;
            }
        });
    }

    protected void afterSave(DtObject contentTemplateDto)
    {
        unbind();
    }

    protected void bindCommonProperties()
    {
        title.setCaption(commonMessages.title());
        DebugIdBuilder.ensureDebugId(title, "title");
        title.setValidationMarker(true);
        title.setMaxLength(ContentUtils.MAX_TITLE_LENGTH);
        validator.validate(title, notEmptyValidatorProvider.get());
        getDisplay().add(title);

        code.setCaption(commonMessages.code());
        DebugIdBuilder.ensureDebugId(code, "code");
        code.setValidationMarker(true);
        code.setMaxLength(ContentUtils.MAX_CODE_LENGTH);
        validator.validate(code, notEmptyValidatorProvider.get());
        getDisplay().add(code);
    }

    protected final void bindContentProperties(@Nullable String contentType)
    {
        if (Objects.equals(contentType, this.contentType))
        {
            return;
        }

        if (null != contentPropertiesPresenter)
        {
            contentPropertiesPresenter.unbind();
            contentPropertiesPresenter = null;
        }

        this.contentType = contentType;
        if (null == contentType)
        {
            return;
        }

        ContentPropertiesPresenter<FlowContent> contentPropertiesPresenter = propertiesPresenterRegistry
                .getPropertiesPresenter(contentType);
        this.contentPropertiesPresenter = contentPropertiesPresenter;
        if (null != contentPropertiesPresenter)
        {
            contentPropertiesPresenter.init(this);
            FlowContent content = getTemplateContent(contentPropertiesPresenter);
            int index = getContentPropertiesIndex();
            for (Property<?> property : contentPropertiesPresenter.bindProperties(content))
            {
                getDisplay().addProperty(property, index++);
            }
            contentPropertiesPresenter.bindHandlers(content);
            TabOrderHelper.updateTabOrder(getDisplay(), false);
        }
    }

    protected void fillTemplate(ContentTemplate template)
    {
        metainfoUtils.setLocalizedValue(template.getTemplate().getCaption(), title.getValue());
        template.setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
    }

    abstract protected int getContentPropertiesIndex();

    abstract protected ContentTemplate getCurrentTemplate();

    abstract protected String getFormTitle();

    abstract protected FlowContent getTemplateContent(ContentPropertiesPresenter<FlowContent> propertiesPresenter);

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(getFormTitle());
        bindCommonProperties();
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), getInitSettingsSetValue());
        getDisplay().display();
    }

    protected String getInitSettingsSetValue()
    {
        return null;
    }

    @Override
    protected void onUnbind()
    {
        if (null != contentPropertiesPresenter)
        {
            contentPropertiesPresenter.unbind();
        }
        super.onUnbind();
    }

    abstract protected void saveTemplate(ContentTemplate template, AsyncCallback<DtObject> callback);
}
