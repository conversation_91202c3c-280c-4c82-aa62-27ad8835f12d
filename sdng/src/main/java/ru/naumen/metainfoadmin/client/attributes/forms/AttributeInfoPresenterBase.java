package ru.naumen.metainfoadmin.client.attributes.forms;

import jakarta.inject.Inject;

import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.forms.InfoDialogDisplayImpl;
import ru.naumen.core.client.forms.InfoDialogPresenterBase;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.Constants.Tags;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Базовый функционал презентера отображения информации об атрибуте (группе атрибутов)
 * @param <T> тип презентера, вложенного в диалоговое окно
 *
 * <AUTHOR>
 * @since 07.09.21
 */
public abstract class AttributeInfoPresenterBase<T extends Presenter> extends InfoDialogPresenterBase<T>
{
    @Inject
    protected AttributesMessages attrMessages;
    protected Context context;

    @Inject
    protected AttributeInfoPresenterBase(InfoDialogDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void setContext(Context context)
    {
        this.context = context;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        getDisplay().addDomHandler(event ->
        {
            Element element = Element.as(event.getNativeEvent().getEventTarget());
            // При нажатии на любую ссылку форма должна быть закрыта
            if (StringUtilities.equalsIgnoreCase(element.getTagName(), Tags.A))
            {
                unbind();
            }
        }, ClickEvent.getType());
    }
}