package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * <AUTHOR>
 *
 */
public class InputmaskModeBindDeletageImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<SelectItem, ListBoxProperty>
{
    @Inject
    private AttributesMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        property.getValueWidget().addItem(messages.inputmaskModeAlias(), StringAttributeType.INPUT_MASK_ALIAS);
        property.getValueWidget().addItem(messages.inputmaskModeDefinitions(),
                StringAttributeType.INPUT_MASK_DEFINITIONS);
        property.getValueWidget().addItem(messages.inputmaskModeRegex(), StringAttributeType.INPUT_MASK_REGEX);
        callback.onSuccess(null);
    }
}