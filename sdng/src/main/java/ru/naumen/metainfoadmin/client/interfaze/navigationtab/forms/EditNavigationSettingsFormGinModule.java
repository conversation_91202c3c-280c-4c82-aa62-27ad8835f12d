package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncImpl;
import ru.naumen.core.client.widgets.select2.SelectListGinModule;
import ru.naumen.core.client.widgets.select2.extractor.SelectListCodeExtractorHasCodeImpl;
import ru.naumen.core.client.widgets.select2.extractor.SelectListExtractorIHasI18NTitleImpl;
import ru.naumen.core.client.widgets.select2.extractor.SelectListExtractorITitledImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandler;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.AddButtonPropertyControllerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.AddLeftMenuItemFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.AddTopMenuItemFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.AttrChainMenuItemControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.AttrChainMenuItemControllerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.EditLeftMenuItemFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.EditTopMenuItemFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.LeftMenuItemPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.LeftMenuItemPropertyDescriptorFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.LeftNavigationMenuPropertiesGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuItemPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuItemPropertyDescriptorFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentCommonVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentCommonVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.quickaccess.AddQuickAccessTileFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.quickaccess.EditQuickAccessTileFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.quickaccess.QuickAccessTilePropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.quickaccess.QuickAccessTilePropertyDescriptorFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.tree.FastCreateTreeGinModule;

/**
 * <AUTHOR>
 * @since 01.04.2013
 */
public class EditNavigationSettingsFormGinModule extends AbstractGinModule
{
    public interface MenuItemContextValueCode
    {
        String SETTINGS = "navigationSettings";
        String MENU_ITEM = "menuItemType";
        String PARENT_PROFILES = "parentProfiles";
        String FQNS = "addButtonFqns";
    }

    public static class MenuSettingsPropertyCode
    {
        private MenuSettingsPropertyCode()
        {
        }

        public static final String ICON = "MenuSettingsIcon";
        public static final String ABBREVIATION = "MenuSettingsAbbreviation";
        public static final String PRESENTATION = "MenuSettingsPresentation";
        public static final String PROFILES = "MenuSettingsProfiles";
        public static final String TAGS = "MenuSettingsTags";
        public static final String SETTINGS_SET = "settingsSet";
    }

    public static class MenuItemPropertyCode
    {
        private MenuItemPropertyCode()
        {
        }

        public static final String TITLE = "menuItemTitle";
        public static final String ATTR_FOR_USE_IN_TITLE = "menuItemAttrForUseInTitle";
        public static final String USE_ATTR_TITLE = "menuItemUseAttrTitle";
        public static final String TYPE = "menuItemType";
        public static final String PARENT = "menuItemParent";
        public static final String TYPE_OF_CARD = "menuItemTypeOfCard";
        public static final String ADD_BUTTON_VALUE = "MenuItemForAddButton";
        public static final String REFERENCE_TAB_VALUE = "MenuItemForReferenceTab";
        public static final String REFERENCE_UI_TEMPLATE = "MenuItemForReferenceTemplate";

        public static final String FORMATTING = "MenuItemFormatting";
        public static final String CUSTOM_LINK_VALUE = "MenuItemForCustomLink";
        public static final String CUSTOM_SYSTEM_LINK_VALUE = "MenuItemForCustomLinkSystem";
        public static final String NEW_TAB_VALUE = "MenuItemNewTab";
        public static final String SETTINGS_SET = "settingsSet";
    }

    public interface QuickAccessTilePropertyCode
    {
        String MENU_ITEM = "menuItem";
        String HINT = "tileHint";
        String AREA = "tileArea";
    }

    @Override
    protected void configure()
    {
        install(new FastCreateTreeGinModule());
        install(new LeftNavigationMenuPropertiesGinModule());

        //@formatter:off
        install(PropertyControllerGinModule.create(MenuItem.class, ObjectFormEdit.class)
            .setPropertyControllerFactory(new TypeLiteral<MenuItemPropertyControllerFactoryImpl>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<MenuItemPropertyDescriptorFactoryImpl>(){}));

        install(PropertyControllerGinModule.create(LeftMenuItemSettingsDTO.class, ObjectFormEdit.class)
                .setPropertyControllerFactory(new TypeLiteral<LeftMenuItemPropertyControllerFactoryImpl>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<LeftMenuItemPropertyDescriptorFactoryImpl>(){}));

        install(PropertyControllerGinModule.create(QuickAccessTileDTO.class, ObjectFormEdit.class)
                .setPropertyControllerFactory(new TypeLiteral<QuickAccessTilePropertyControllerFactoryImpl>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<QuickAccessTilePropertyDescriptorFactoryImpl>(){}));


        install(SelectListGinModule.create(MenuItem.class)
                .setCodeExtractor(new TypeLiteral<SelectListCodeExtractorHasCodeImpl<MenuItem>>(){})
                .setTitleExtractor(new TypeLiteral<SelectListExtractorIHasI18NTitleImpl<MenuItem>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(PropertyController.class, new TypeLiteral<PropertyControllerSyncImpl<SelectItem, SingleSelectProperty<MenuItem>>>(){})
            .build(new TypeLiteral<PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<MenuItem>>>(){}));

        install(SelectListGinModule.create(LeftMenuItemSettingsDTO.class)
                .setCodeExtractor(new TypeLiteral<SelectListCodeExtractorHasCodeImpl<LeftMenuItemSettingsDTO>>(){})
                .setTitleExtractor(new TypeLiteral<SelectListExtractorIHasI18NTitleImpl<LeftMenuItemSettingsDTO>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, new TypeLiteral<PropertyControllerSyncImpl<SelectItem, SingleSelectProperty<LeftMenuItemSettingsDTO>>>(){})
                .build(new TypeLiteral<PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<LeftMenuItemSettingsDTO>>>(){}));
        
        install(SelectListGinModule.create(Reference.class)
                .setCodeExtractor(new TypeLiteral<SelectListCodeExtractorHasCodeImpl<Reference>>(){})
                .setTitleExtractor(new TypeLiteral<SelectListExtractorITitledImpl<Reference>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(PropertyController.class, new TypeLiteral<PropertyControllerSyncImpl<SelectItem, SingleSelectProperty<Reference>>>(){})
            .build(new TypeLiteral<PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<Reference>>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, new TypeLiteral<PropertyControllerSyncImpl<SelectItem, SingleSelectProperty<SelectItem>>>(){})
                .build(new TypeLiteral<PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<SelectItem>>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, AttrChainMenuItemControllerImpl.class)
                .build(new TypeLiteral<AttrChainMenuItemControllerFactory>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(LinkToContentCommonVCHDelegateImpl.class, LinkToContentCommonVCHDelegateImpl.class)
                .build(LinkToContentCommonVCHDelegateFactory.class));
        
        install(new GinFactoryModuleBuilder()
            .implement(PropertyController.class, AddButtonPropertyControllerImpl.class)
            .build(new TypeLiteral<PropertyControllerSyncFactoryInj<Collection<DtObject>, Property<Collection<DtObject>>>>(){}));
        
        bind(new TypeLiteral<ObjectFormAfterBindHandler<ObjectFormAdd, MenuItem>>(){})
            .to(AddTopMenuItemFormAfterBindHandlerImpl.class);
        bind(new TypeLiteral<ObjectFormAfterBindHandler<ObjectFormEdit, MenuItem>>(){})
            .to(EditTopMenuItemFormAfterBindHandlerImpl.class);

        bind(new TypeLiteral<ObjectFormAfterBindHandler<ObjectFormAdd, LeftMenuItemSettingsDTO>>(){})
                .to(AddLeftMenuItemFormAfterBindHandlerImpl.class);
        bind(new TypeLiteral<ObjectFormAfterBindHandler<ObjectFormEdit, LeftMenuItemSettingsDTO>>(){})
                .to(EditLeftMenuItemFormAfterBindHandlerImpl.class);

        bind(new TypeLiteral<ObjectFormAfterBindHandler<ObjectFormAdd, QuickAccessTileDTO>>(){})
                .to(AddQuickAccessTileFormAfterBindHandlerImpl.class);
        bind(new TypeLiteral<ObjectFormAfterBindHandler<ObjectFormEdit, QuickAccessTileDTO>>(){})
                .to(EditQuickAccessTileFormAfterBindHandlerImpl.class);
        //@formatter:on
    }
}
