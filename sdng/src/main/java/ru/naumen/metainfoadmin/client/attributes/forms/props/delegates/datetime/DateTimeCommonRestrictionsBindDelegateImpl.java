package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import java.util.Arrays;
import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ValueCellListProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;

/**
 * Делегат биндинга свойства "Значение атрибута допустимо указывать"
 * <AUTHOR>
 * @since 7 дек. 2018 г.
 */
public class DateTimeCommonRestrictionsBindDelegateImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>
        implements
        AttributeFormPropertyDelegateBind<F, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>>
{

    @Override
    public void bindProperty(PropertyContainerContext context, ValueCellListProperty<CommonRestriction> property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().setAcceptableValues(Arrays.asList(CommonRestriction.values()));
        property.setValue(Arrays.asList(CommonRestriction.values()));
        super.bindProperty(context, property, callback);
    }

}
