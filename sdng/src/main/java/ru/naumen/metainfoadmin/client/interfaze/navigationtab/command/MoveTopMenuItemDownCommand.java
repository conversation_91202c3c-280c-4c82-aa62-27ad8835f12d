package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;

/**
 * Смещение элемента верхнего меню вниз
 * <AUTHOR>
 * @since 19.06.2020
 */
public class MoveTopMenuItemDownCommand extends MoveMenuItemDownCommand<MenuItem>
{
    public static final String ID = "moveTopMenuItemDown";

    @Inject
    public MoveTopMenuItemDownCommand(@Assisted NavigationSettingsTMCommandParam param)
    {
        super(param);
    }

    @Override
    protected MoveNavigationMenuItemAction getAction()
    {
        return new MoveNavigationMenuItemAction();
    }
}
