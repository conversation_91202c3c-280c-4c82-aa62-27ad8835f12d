package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Класс с логикой обработки полученного метакласса для свойства "Прямая ссылка" в атрибуте "Обратная ссылка". <br>
 * <strong>ВАЖНО:</strong> После заполнения данных в контексте вызывает обновление свойства "Редактируемый".
 *
 * <AUTHOR>
 * @since 01.11.2023
 */
public class GetMetaClassCallback extends GetMetaClassBaseCallback
{
    public GetMetaClassCallback(PropertyContainerContext context, String directLinkAttrCode)
    {
        super(context, directLinkAttrCode);
    }

    @Override
    protected void handleSuccess(MetaClass metaClass)
    {
        super.handleSuccess(metaClass);
        //Цель обновления свойства "Редактируемый" после получения метакласса для свойства "Прямая ссылка" не ясна,
        // но на форме редактирования вызывала очистку свойства "Редактируемый в списках"
        fireEditableValueChanged();
    }

    private void fireEditableValueChanged()
    {
        PropertyController propertyController = context.getPropertyControllers()
                .get(AttributeFormPropertyCode.EDITABLE);
        if (null != propertyController)
        {
            propertyController.fireValueChangeEvent();
        }
    }
}
