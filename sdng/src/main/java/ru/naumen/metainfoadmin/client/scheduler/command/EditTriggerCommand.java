package ru.naumen.metainfoadmin.client.scheduler.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.scheduler.forms.TriggerFormPresenter;

/**
 * Команда редактирования правила выполнения задачи планировщика.
 * <AUTHOR>
 */
public class EditTriggerCommand extends TriggerPresenterCommandBase
{
    @Inject
    protected SchedulerGinjector injector;

    @Inject
    public EditTriggerCommand(@Assisted TriggerCommandParam param)
    {
        super(param);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected TriggerFormPresenter getTriggerFormPresenter()
    {
        return injector.editTriggerFormPresenter();
    }
}
