package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.dispatch2.DirectLinkAttributeDescription;
import ru.naumen.metainfo.shared.dispatch2.FindDirectLinkAttributesResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.DirectLinkValueFormatter;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public abstract class DirectLinkRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    protected class RefreshCallback extends BasicCallback<FindDirectLinkAttributesResponse>
    {
        protected final AsyncCallback<Boolean> callback;
        protected final ListBoxProperty property;
        protected final PropertyContainerContext context;

        public RefreshCallback(AsyncCallback<Boolean> callback, ListBoxProperty property,
                PropertyContainerContext context)
        {
            this.callback = callback;
            this.property = property;
            this.context = context;
        }

        protected void fillProperty(Collection<DirectLinkAttributeDescription> attrDescs)
        {
            SingleSelectCellList<String> selectList = property.getValueWidget();
            selectList.clear();
            for (DirectLinkAttributeDescription attrDesc : attrDescs)
            {
                selectList.addItem(attrDesc.getAttrTitle(), valueFormatter.to(attrDesc));
            }
        }

        protected void getMetaClass(Collection<DirectLinkAttributeDescription> attrDescs)
        {
            if (!attrDescs.isEmpty())
            {
                DirectLinkAttributeDescription attrDesc = attrDescs.iterator().next();
                metainfoService.getMetaClass(attrDesc.getFqn(),
                        new GetMetaClassCallback(context, attrDesc.getAttrCode())
                        {
                            @Override
                            protected void handleSuccess(MetaClass metaClass)
                            {
                                super.handleSuccess(metaClass);
                                callback.onSuccess(true);
                            }
                        });
            }
            else
            {
                callback.onSuccess(true);
            }
        }

        @Override
        protected void handleSuccess(FindDirectLinkAttributesResponse response)
        {
            Collection<DirectLinkAttributeDescription> attrDescs = Collections2
                    .filter(response.getAttrs(), DESC_FILTER);
            fillProperty(attrDescs);
            setPropertyValue();
            getMetaClass(attrDescs);
        }

        protected void setPropertyValue()
        {
            SingleSelectCellList<String> selectList = property.getValueWidget();
            context.getPropertyValues()
                    .setProperty(AttributeFormPropertyCode.DIRECT_LINK_TARGET,
                            SelectItemValueExtractor.extract(selectList.getItem(0)));
        }
    }

    protected Predicate<DirectLinkAttributeDescription> DESC_FILTER = new Predicate<DirectLinkAttributeDescription>()
    {
        @Override
        public boolean apply(DirectLinkAttributeDescription input)
        {
            return !Root.FQN.equals(input.getFqn());
        }
    };

    @Inject
    protected DirectLinkValueFormatter valueFormatter;
    @Inject
    AdminMetainfoServiceAsync metainfoService;
}
