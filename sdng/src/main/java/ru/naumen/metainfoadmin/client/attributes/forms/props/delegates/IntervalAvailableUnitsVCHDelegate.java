package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

public class IntervalAvailableUnitsVCHDelegate<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(Lists.newArrayList(DEFAULT_VALUE));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
