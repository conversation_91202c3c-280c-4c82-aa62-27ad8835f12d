package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import java.util.Collection;
import java.util.Comparator;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.DateTimeIntervalMessages;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;

/**
 * Делегат, осуществляющий биндинг свойства "Единицы измерения, доступные при редактировании"
 * при редактировании атрибута
 * <AUTHOR>
 * @since 29.03.2017
 */
public class IntervalAvailableUnitsBindDelegateImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<Collection<SelectItem>, MultiSelectBoxProperty>
        implements AttributeFormPropertyDelegateBind<F, Collection<SelectItem>, MultiSelectBoxProperty>
{
    @Inject
    private DateTimeIntervalMessages messages;

    public final Comparator<ITitled> DATE_TIME_INTERVAL_COMPARATOR = (o1, o2) ->
    {
        if (o1 instanceof SelectItem && o2 instanceof SelectItem)
        {
            return ObjectUtils.compare(Interval.valueOf(((SelectItem)o1).getCode()).getCode(),
                    Interval.valueOf(((SelectItem)o2).getCode()).getCode());
        }
        return 0;
    };

    @Override
    public void bindProperty(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().getSelectedItemsPanel().setComparator(DATE_TIME_INTERVAL_COMPARATOR);
        for (Interval interval : Interval.values())
        {
            property.getValueWidget().addItem(messages.suffix(10, interval), interval.name());
        }
        super.bindProperty(context, property, callback);
    }
}
