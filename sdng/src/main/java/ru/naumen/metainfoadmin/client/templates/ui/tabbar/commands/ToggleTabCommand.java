package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import java.util.Iterator;
import java.util.List;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabModel;

/**
 * Команда включения/выключения вкладки в шаблоне панели вкладок.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class ToggleTabCommand extends BaseCommandImpl<TabModel, Void>
{
    private final List<TabModel> tabs;

    @Inject
    public ToggleTabCommand(@Assisted TabModelCommandParam param)
    {
        super(param);
        this.tabs = param.getTabs();
    }

    @Override
    public void execute(CommandParam<TabModel, Void> param)
    {
        TabModel tabModel = param.getValue();
        if (!tabModel.getCode().isEmpty())
        {
            tabModel.setEnabled(!tabModel.isEnabled());
        }
        else if (isOnlyOneTabSelected())
        {
            tabs.stream().skip(1).forEach(tab -> tab.setEnabled(true));
        }
        else if (tabs.size() > 1)
        {
            Iterator<TabModel> tabIterator = tabs.iterator();
            while (tabIterator.hasNext())
            {
                if (tabIterator.next().isEnabled())
                {
                    break;
                }
            }
            while (tabIterator.hasNext())
            {
                tabIterator.next().setEnabled(false);
            }
        }
        param.getCallbackSafe().onSuccess(null);
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof TabModel))
        {
            return false;
        }
        TabModel tab = (TabModel)input;
        return tab.getCode().isEmpty() || !tab.isEnabled() || !isOnlyOneTabSelected();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH;
    }

    private boolean isOnlyOneTabSelected()
    {
        return tabs.stream().skip(1).filter(TabModel::isEnabled).limit(2).count() <= 1;
    }
}
