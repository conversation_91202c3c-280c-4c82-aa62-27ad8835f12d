package ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Пераметр для команд, совершающих действия для мест использования шаблона списка
 * <AUTHOR>
 * @since 21.08.2018
 */
public class ListTemplateUsageCommandParam extends CommandParam<Collection<DtObject>, Void>
{
    private String templateCode;

    public ListTemplateUsageCommandParam(String templateCode, Collection<DtObject> values, AsyncCallback<Void> callback)
    {
        super(values, callback);
        this.templateCode = templateCode;
    }

    public String getTemplateCode()
    {
        return templateCode;
    }
}
