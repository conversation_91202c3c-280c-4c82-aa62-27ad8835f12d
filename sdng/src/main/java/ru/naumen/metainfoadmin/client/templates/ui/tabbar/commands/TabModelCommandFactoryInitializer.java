package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;

/**
 * Компонент для инициализации команд.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
@Singleton
public class TabModelCommandFactoryInitializer
{
    @Inject
    public TabModelCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<ToggleTabCommand, TabModelCommandParam> enableTabCommandProvider,
            CommandProvider<EditTabCommand, TabModelCommandParam> editTabCommandProvider,
            CommandProvider<MoveTabUpCommand, TabModelCommandParam> moveTabUpCommandProvider,
            CommandProvider<MoveTabDownCommand, TabModelCommandParam> moveTabDownCommandProvider)
    {
        factory.register(TabModelCommandCodes.TOGGLE_TAB, enableTabCommandProvider);
        factory.register(TabModelCommandCodes.EDIT_TAB, editTabCommandProvider);
        factory.register(TabModelCommandCodes.MOVE_TAB_UP, moveTabUpCommandProvider);
        factory.register(TabModelCommandCodes.MOVE_TAB_DOWN, moveTabDownCommandProvider);
    }
}
