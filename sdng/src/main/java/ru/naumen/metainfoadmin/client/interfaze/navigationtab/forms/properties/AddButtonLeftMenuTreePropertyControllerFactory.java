package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;

import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Фабрика контроллеров свойства "Класс объектов для добавления"
 * <AUTHOR>
 * @since 15.12.2021
 */
public interface AddButtonLeftMenuTreePropertyControllerFactory
        extends PropertyControllerSyncFactoryInj<Collection<DtObject>, PropertyBase<Collection<DtObject>,
        PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly>>>
{
}
