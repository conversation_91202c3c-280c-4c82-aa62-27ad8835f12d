package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms;

import static ru.naumen.metainfo.shared.Constants.HomePage.*;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.ATTRIBUTE_CHAIN;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CASES;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CLASS;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.REFERENCE_VALUE;

import java.util.Arrays;
import java.util.List;

/**
 * Значения-константы, определяющие список атрибутов на форме добавления и редактирования домашней страницы
 * <AUTHOR>
 * @since 14.01.2023
 */
public interface HomePageFormConstants
{
    /**
     * Список кодов на формах добавления\редактирования домашней страницы.
     */
    List<String> PROPERTIES = Arrays.asList(
            TYPE,
            TITLE,
            CUSTOM_LINK_VALUE,
            PROFILES,
            REFERENCE_CARD_TYPE,
            ATTRIBUTE_CHAIN,
            OBJECT_CLASS,
            OBJECT_CASES,
            REFERENCE_VALUE,
            REFERENCE_TAB_VALUE,
            REFERENCE_UI_TEMPLATE,
            TAGS,
            SETTINGS_SET);
}