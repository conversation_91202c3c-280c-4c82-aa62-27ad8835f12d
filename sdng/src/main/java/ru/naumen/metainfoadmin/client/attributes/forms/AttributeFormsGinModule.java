package ru.naumen.metainfoadmin.client.attributes.forms;

import com.google.gwt.inject.client.AbstractGinModule;

import ru.naumen.metainfoadmin.client.attributes.forms.add.AttributeFormAddGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.edit.AttributeFormEditGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.props.AttributeFormPropertiesGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.usage.ShowUsageGinModule;

/**
 * <AUTHOR>
 * @since 04.04.2012
 */
public class AttributeFormsGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        install(new AttributeFormAddGinModule());
        install(new AttributeFormEditGinModule());
        install(new AttributeFormPropertiesGinModule());
        install(new ShowUsageGinModule());
    }
}
