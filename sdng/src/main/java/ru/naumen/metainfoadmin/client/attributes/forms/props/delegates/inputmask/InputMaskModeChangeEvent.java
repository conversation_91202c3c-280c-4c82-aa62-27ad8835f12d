package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие изменения режима маски ввода на форме добавления/редактирования атрибута
 *
 * <AUTHOR>
 * @since 05.11.2020
 */
public class InputMaskModeChangeEvent extends GwtEvent<InputMaskModeChangeHandler>
{
    private static final Type<InputMaskModeChangeHandler> TYPE = new Type<>();

    private final String inputMaskMode;

    public InputMaskModeChangeEvent(String inputMaskMode)
    {
        this.inputMaskMode = inputMaskMode;
    }

    @Override
    public Type<InputMaskModeChangeHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(InputMaskModeChangeHandler handler)
    {
        handler.onInputMaskModeChange(this);
    }

    public static Type<InputMaskModeChangeHandler> getType()
    {
        return TYPE;
    }

    public String getInputMaskMode()
    {
        return inputMaskMode;
    }
}
