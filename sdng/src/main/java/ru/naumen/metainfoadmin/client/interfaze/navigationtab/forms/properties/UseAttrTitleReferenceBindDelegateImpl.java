package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Делегат отрисовки для виджета {@link MenuItemPropertyCode#USE_ATTR_TITLE}
 *
 * <AUTHOR>
 * @since 12.03.2022
 */
public class UseAttrTitleReferenceBindDelegateImpl extends
        PropertyDelegateBindImpl<Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void bindProperty(final PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Void> callback)
    {
        boolean useAttrTitle = Boolean.TRUE.equals(
                context.getPropertyValues().getProperty(MenuItemPropertyCode.USE_ATTR_TITLE));
        property.setValue(useAttrTitle);
        callback.onSuccess(null);
    }
}
