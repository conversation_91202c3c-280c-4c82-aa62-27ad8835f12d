package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.requiredInInterface;

import static ru.naumen.metainfo.shared.Constants.ATTR_TYPES_WITH_REQUIRED_IN_INTERFACE_ABILITY;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.REQUIRED;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.REQUIRED_IN_INTERFACE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 */
public class RequiredInInterfaceRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        Boolean isRequired = context.getPropertyValues().getProperty(REQUIRED);
        if (isRequired)
        {
            context.setPropertyEnabled(REQUIRED_IN_INTERFACE, false);
        }
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        boolean suitableAttrType = ATTR_TYPES_WITH_REQUIRED_IN_INTERFACE_ABILITY.contains(attrType);
        callback.onSuccess(suitableAttrType && !computable);
    }
}
