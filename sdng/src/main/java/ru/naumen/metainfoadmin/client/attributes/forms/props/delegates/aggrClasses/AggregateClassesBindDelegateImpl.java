package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.aggrClasses;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;

import java.io.Serializable;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ValueCellListProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.CommonUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;

/**
 * Делегат биндинга свойства "Агрегируемые типы" - получает с сервера метаинфу для Отдела, Команды и Сотрудника
 * и заполняет виджет по получении ответа
 * <AUTHOR>
 * @since 17.05.2012
 */
public class AggregateClassesBindDelegateImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<Collection<MetaClassLite>, ValueCellListProperty<MetaClassLite>>
        implements AttributeFormPropertyDelegateBind<F, Collection<MetaClassLite>, ValueCellListProperty<MetaClassLite>>
{
    public static class MetaClassComparator implements Comparator<MetaClassLite>, Serializable
    {
        private static final long serialVersionUID = -2676149098206385039L;
        private final List<ClassFqn> fqns;

        public MetaClassComparator(List<ClassFqn> fqns)
        {
            this.fqns = fqns;
        }

        @Override
        public int compare(MetaClassLite o1, MetaClassLite o2)
        {
            if (fqns.indexOf(o1.getFqn()) < fqns.indexOf(o2.getFqn()))
            {
                return -1;
            }
            else
            {
                return 1;
            }
        }
    }

    private class GetMetaClassesCallback extends BasicCallback<List<MetaClassLite>>
    {
        private final ValueCellListProperty<MetaClassLite> property;
        private final AsyncCallback<Void> callback;
        private final boolean initValue;

        public GetMetaClassesCallback(ValueCellListProperty<MetaClassLite> property, boolean initValue,
                AsyncCallback<Void> callback)
        {
            this.property = property;
            this.callback = callback;
            this.initValue = initValue;
        }

        @Override
        protected void handleSuccess(List<MetaClassLite> metaClasses)
        {
            metaClasses.sort(new MetaClassComparator(AggregateAttributeType.CLASS_FQNS));
            property.getValueWidget().setAcceptableValues(metaClasses);
            if (initValue)
            {
                property.setValue(metaClasses.stream()
                                .filter(LEAVE_EMPLOYEE_PREDICATE)
                                .collect(Collectors.toList()),
                        false);
            }
            callback.onSuccess(null);
        }
    }

    private static Predicate<MetaClassLite> LEAVE_EMPLOYEE_PREDICATE = new Predicate<MetaClassLite>()
    {
        @Override
        public boolean test(MetaClassLite input)
        {
            return Employee.FQN.equals(input.getFqn());
        }
    };

    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    CommonUtils commonUtils;

    @Override
    public void bindProperty(PropertyContainerContext context, ValueCellListProperty<MetaClassLite> property,
            final AsyncCallback<Void> callback)
    {
        Attribute attribute = context.getContextValues().getProperty(ATTRIBUTE);
        metainfoService.getMetaClasses(AggregateAttributeType.CLASS_FQNS,
                new GetMetaClassesCallback(property, attribute == null, callback));
    }
}
