package ru.naumen.metainfoadmin.client.templates.ui;

import java.util.List;

import com.google.gwt.user.client.ui.RootPanel;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.client.ui.template.creation.UITemplateCreationService;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.templates.ui.UITemplate;
import ru.naumen.metainfo.shared.templates.ui.UITemplateDto;
import ru.naumen.metainfo.shared.templates.ui.UITemplateUtils;
import ru.naumen.metainfo.shared.templates.ui.WindowTemplate;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.reference.UIComponentReference;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ReloadUIEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;

/**
 * Утилитарные методы для работы с шаблонами в интерфейсе администратора.
 * <AUTHOR>
 * @since Jul 30, 2021
 */
@Singleton
public class UITemplateHelper
{
    private final UITemplateCreationService templateCreationService;
    private final WidgetResources widgetResources;
    private final UITemplateMetainfoServiceAsync templateMetainfoService;

    @Inject
    public UITemplateHelper(
            UITemplateCreationService templateCreationService,
            WidgetResources widgetResources,
            UITemplateMetainfoServiceAsync templateMetainfoService)
    {
        this.templateCreationService = templateCreationService;
        this.widgetResources = widgetResources;
        this.templateMetainfoService = templateMetainfoService;
    }

    public void applyEmptyTemplate(UIContext context)
    {
        Content originalContent = getOriginalContent(context);
        WindowTemplate windowTemplate = (WindowTemplate)templateCreationService.create(originalContent);
        UITemplate template = new UITemplate();
        template.setContent(null == windowTemplate ? new WindowTemplate() : windowTemplate);
        template.setFormCode(context.getCode());
        template.setClassFqn(UITemplateUtils.getTemplateDeclaredFqn(context.getMetainfo().getFqn(),
                context.getRootContentInfo()));
        context.getEventBus().fireEvent(new ChangeUITemplateModeEvent(template, true));

        templateMetainfoService.previewContent(template, context.getMetainfo().getFqn(),
                context.getCode(), new BasicCallback<Content>(context.getReadyState())
                {
                    @Override
                    protected void handleSuccess(Content content)
                    {
                        ContentInfo contentInfo = createContentInfo(context, content);
                        context.getEventBus().fireEvent(new ReloadUIEvent(contentInfo));
                    }
                });
    }

    public ContentInfo createContentInfo(UIContext context, Content content)
    {
        ContentInfo oldContentInfo = context.getRootContentInfo();
        return new ContentInfo(oldContentInfo.getDeclaredMetaclass(), oldContentInfo.getFormId(), content);
    }

    public Content getOriginalContent(UIContext context)
    {
        return null == context.getUITemplateContext() ? context.getRootContent() :
                context.getUITemplateContext().getOriginalContent();
    }

    public void updateAvailableTemplates(UIContext context, List<UITemplateDto> availableTemplates)
    {
        UITemplateContext templateContext = context.getUITemplateContext();
        if (null != templateContext)
        {
            templateContext.setAvailableTemplates(availableTemplates);
        }
    }

    /**
     * Обновляет шаблон в соответствии с изменениями в контексте карточки.
     * Если на карточке были добавлены новые панели вкладок, в шаблоне появятся соответствующие ссылки.
     * @param template обновляемый шаблон
     * @param context контекст карточки
     */
    public void updateTemplateFromContext(UITemplate template, UIContext context)
    {
        UIComponentReference originalReference = templateCreationService.create(getOriginalContent(context));
        if (originalReference != null)
        {
            UITemplateUtils.mergeReferences(originalReference, template.getContent());
        }
    }

    public void reloadOriginalUI(UIContext context)
    {
        Content originalContent = getOriginalContent(context);
        context.getEventBus().fireEvent(new ReloadUIEvent(createContentInfo(context, originalContent)));
    }

    public void setTemplate(UIContext context, @Nullable UITemplate template, boolean isNew)
    {
        if (null == template)
        {
            context.setUITemplateContext(null);
            return;
        }
        UITemplateContext existingContext = context.getUITemplateContext();
        if (null != existingContext)
        {
            existingContext.setTemplate(template);
            existingContext.setNewTemplate(isNew);
        }
        else
        {
            UITemplateContext newContext = new UITemplateContext(template, context.getRootContent());
            newContext.setNewTemplate(isNew);
            context.setUITemplateContext(newContext);
        }
    }

    public void setTemplateMode(boolean enabled)
    {
        RootPanel.getBody().setStyleName(widgetResources.all().templateMode(), enabled);
    }
}
