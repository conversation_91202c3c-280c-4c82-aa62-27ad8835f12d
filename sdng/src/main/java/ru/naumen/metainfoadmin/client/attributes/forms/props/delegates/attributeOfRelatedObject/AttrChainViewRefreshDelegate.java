package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат обновления свойства "Атрибут связи" на форме редактирования.
 * <AUTHOR>
 * @since 08.08.18
 */
public class AttrChainViewRefreshDelegate<F extends ObjectForm>
        implements
        AttributeFormPropertyDelegateRefresh<F, String, TextBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context,
            TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        IProperties propertyValues = context.getPropertyValues();
        String typeCode = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_TYPE);

        if (!typeCode.equals(AttributeOfRelatedObjectSettings.CODE))
        {
            callback.onSuccess(false);
            return;
        }

        context.setPropertyEnabled(AttributeFormPropertyCode.ATTR_CHAIN_VIEW, false);
        property.setDisable();

        callback.onSuccess(true);
    }
}
