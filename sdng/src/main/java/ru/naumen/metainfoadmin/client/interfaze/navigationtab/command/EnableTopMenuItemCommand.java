package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchNavigationMenuItemAction;

/**
 * Команда включения элемента верхнего меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class EnableTopMenuItemCommand extends EnableMenuItemCommand<MenuItem>
{
    public static final String ID = "enableTopMenuItemCommand";

    @Inject
    public EnableTopMenuItemCommand(@Assisted NavigationSettingsTMCommandParam param)
    {
        super(param);
    }

    @Override
    protected SwitchNavigationMenuItemAction getAction()
    {
        return new SwitchNavigationMenuItemAction();
    }
}
