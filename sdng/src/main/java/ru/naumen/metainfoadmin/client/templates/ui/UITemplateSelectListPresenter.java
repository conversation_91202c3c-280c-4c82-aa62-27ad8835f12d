package ru.naumen.metainfoadmin.client.templates.ui;

import com.google.gwt.event.shared.EventBus;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.content.ObjectToolPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.client.ui.template.UITemplateResult;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEventHandler;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateHandler;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateModeHandler;
import ru.naumen.metainfoadmin.client.dynadmin.ReloadUIEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;

/**
 * Представления виджета выбора шаблона.
 * <AUTHOR>
 * @since Jul 29, 2021
 */
public class UITemplateSelectListPresenter extends BasicPresenter<UITemplateSelectListDisplay>
        implements ObjectToolPresenter<TabBar>, ChangeUITemplateModeHandler, ChangeUITemplateHandler,
        ChangeLayoutModeEventHandler
{
    private final UITemplateMetainfoServiceAsync templateMetainfoService;
    private final UITemplateHelper templateHelper;

    private UIContext context;

    @Inject
    public UITemplateSelectListPresenter(UITemplateSelectListDisplay display, EventBus eventBus,
            UITemplateMetainfoServiceAsync templateMetainfoService,
            UITemplateHelper templateHelper)
    {
        super(display, eventBus);
        this.templateMetainfoService = templateMetainfoService;
        this.templateHelper = templateHelper;
    }

    public void init(UIContext context)
    {
        this.context = context;
    }

    @Override
    public void onChangeLayoutMode(ChangeLayoutModeEvent e)
    {
        getDisplay().setEnabled(!e.isLayoutModeEnabled());
    }

    @Override
    public void onTemplateChanged(ChangeUITemplateEvent event)
    {
        UITemplateContext templateContext = context.getUITemplateContext();
        if (null != templateContext)
        {
            templateContext.setTemplateChanged(event.isChanged());
        }
        refreshDisplay();
    }

    @Override
    public void onTemplateModeChanged(ChangeUITemplateModeEvent event)
    {
        UITemplateContext templateContext = context.getUITemplateContext();
        if (null != templateContext)
        {
            templateContext.setTemplateChanged(false);
        }
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        UITemplateContext templateContext = context.getUITemplateContext();
        getDisplay().asWidget().setVisible(null != templateContext);
        getDisplay().setModified(null != templateContext && templateContext.isTemplateChanged());
        refreshItems();
    }

    @Override
    public void refreshDisplay(@Nullable TabBar obj)
    {
        refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        registerHandler(context.getEventBus().addHandler(ChangeUITemplateModeEvent.TYPE, this));
        registerHandler(context.getEventBus().addHandler(ChangeUITemplateEvent.TYPE, this));
        registerHandler(context.getEventBus().addHandler(ChangeLayoutModeEvent.getType(), this));
        registerHandler(getDisplay().addValueChangeHandler(event -> loadTemplate(event.getValue())));
        refreshDisplay();
    }

    private void loadTemplate(@Nullable String templateCode)
    {
        if (null != templateCode)
        {
            templateMetainfoService.loadTemplate(context.getMetainfo().getFqn(), context.getCode(), templateCode,
                    new BasicCallback<UITemplateResult>(context.getReadyState())
                    {
                        @Override
                        protected void handleSuccess(UITemplateResult result)
                        {
                            templateHelper.updateTemplateFromContext(result.getTemplate(), context);
                            context.getEventBus().fireEvent(new ChangeUITemplateModeEvent(result.getTemplate(), false));
                            context.getEventBus().fireEvent(new ReloadUIEvent(templateHelper.createContentInfo(
                                    context, result.getContent())));
                        }
                    });
        }
        else
        {
            templateHelper.applyEmptyTemplate(context);
        }
    }

    private void refreshItems()
    {
        getDisplay().clear();
        UITemplateContext templateContext = context.getUITemplateContext();
        if (null == templateContext)
        {
            getDisplay().setSelectedItemCode(null, false);
        }
        else
        {
            templateContext.getAvailableTemplates().forEach(item -> getDisplay().addItem(
                    new SimpleDtObject(item.getCode(), item.getTitle()), item.isDefault()));
            String templateCode = templateContext.isNewTemplate() ? null : templateContext.getTemplate().getCode();
            getDisplay().setSelectedItemCode(templateCode, false);
        }
    }
}
