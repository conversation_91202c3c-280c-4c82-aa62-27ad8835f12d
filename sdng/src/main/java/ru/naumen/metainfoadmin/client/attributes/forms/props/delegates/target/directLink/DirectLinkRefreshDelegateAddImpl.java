package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class DirectLinkRefreshDelegateAddImpl<F extends ObjectForm> extends DirectLinkRefreshDelegateImpl<F>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (!BackLinkAttributeType.CODE.equals(typeCode))
        {
            callback.onSuccess(false);
            return;
        }

        MetaClass metaClass = context.getContextValues().<MetaClass> getProperty(AttributeFormContextValues.METAINFO);
        metainfoService.findDirectLinkAttributes(metaClass.getFqn(), new RefreshCallback(callback, property, context));
    }
}
