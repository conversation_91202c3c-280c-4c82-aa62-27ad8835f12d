package ru.naumen.metainfoadmin.client.templates.content.card;

import java.util.Date;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.common.client.utils.OnStartCallbackDecorator;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.TitledClassFqn;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentInfoPropertiesPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentInfoPropertiesPresenterRegistry;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentTitles;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateMessages;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplatesPlace;
import ru.naumen.metainfoadmin.client.templates.content.command.ContentTemplateCommandCode;

/**
 * Представление параметров на карточке шаблона контента.
 * <AUTHOR>
 * @since Mar 23, 2021
 */
public class ContentTemplateInfoPresenter extends BasicPresenter<InfoDisplay>
{
    private class ContentTemplateCommandParam<C> extends CommandParam<DtObject, C>
    {
        public ContentTemplateCommandParam(AsyncCallback<C> callback)
        {
            super(valueSource, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> contentType;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> classFqn;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> profiles;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> creationTime;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> lastModificationTime;
    private Property<String> settingsSet;

    @Inject
    private CommonMessages commonMessages;
    @Inject
    private ContentTemplateMessages messages;
    @Inject
    private ContentTitles contentTitles;
    @Inject
    private AdminDialogMessages dialogMessages;
    @Inject
    private Formatters formatters;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    private ContentInfoPropertiesPresenterRegistry propertiesPresenterRegistry;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    private final ToolBarDisplayMediator<DtObject> toolBar;
    private final OnStartCallback<Void> removeCallback = new OnStartBasicCallback<Void>()
    {
        @Override
        protected void handleSuccess(Void value)
        {
            placeController.goTo(ContentTemplatesPlace.INSTANCE);
        }
    };
    private OnStartCallback<DtObject> refreshCallback;
    private ContentInfoPropertiesPresenter<FlowContent> contentInfoPresenter;

    private DtObject contentTemplateDto;
    private final ValueSource<DtObject> valueSource = () -> contentTemplateDto;

    @Inject
    public ContentTemplateInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
    }

    public void init(DtObject contentTemplateDto, OnStartCallback<DtObject> refreshCallback)
    {
        this.contentTemplateDto = contentTemplateDto;
        this.refreshCallback = new OnStartCallbackDecorator<DtObject, DtObject>(refreshCallback)
        {
            @Override
            protected DtObject apply(DtObject from)
            {
                ContentTemplateInfoPresenter.this.contentTemplateDto = from;
                return from;
            }
        };
    }

    @Override
    public void refreshDisplay()
    {
        toolBar.refresh(contentTemplateDto);
        setPropertyValues();
    }

    public void setContentTemplateDto(DtObject contentTemplateDto)
    {
        this.contentTemplateDto = contentTemplateDto;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(messages.templateParameters());
        bindToolBar();
        bindCommonProperties();
        bindContentProperties();
        bindTimeProperties();
        settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());
        refreshDisplay();
    }

    @SuppressWarnings("unchecked")
    private <C> void addTool(String button, String title, String command, CommandParam<DtObject, C> param,
            Predicate<DtObject> predicate)
    {
        ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(button, title,
                command, param);
        buttonPresenter.addPossibleFilter(predicate);
        toolBar.add(buttonPresenter);
    }

    private void bindCommonProperties()
    {
        title.setCaption(commonMessages.title());
        DebugIdBuilder.ensureDebugId(title, "title");
        getDisplay().add(title);

        code.setCaption(commonMessages.code());
        DebugIdBuilder.ensureDebugId(code, "code");
        getDisplay().add(code);

        contentType.setCaption(dialogMessages.contentType());
        DebugIdBuilder.ensureDebugId(contentType, "contentType");
        getDisplay().add(contentType);

        classFqn.setCaption(commonMessages.clazz());
        DebugIdBuilder.ensureDebugId(classFqn, "classFqn");
        getDisplay().add(classFqn);

        profiles.setCaption(dialogMessages.contentProfiles());
        DebugIdBuilder.ensureDebugId(profiles, "profiles");
        getDisplay().add(profiles);
    }

    private void bindContentProperties()
    {
        String contentType = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.CONTENT_TYPE);
        if (null == contentType)
        {
            return;
        }
        contentInfoPresenter = propertiesPresenterRegistry.getPropertiesPresenter(contentType);
        contentInfoPresenter.bindProperties(getDisplay());
    }

    private void bindTimeProperties()
    {
        creationTime.setCaption(messages.creationTime());
        DebugIdBuilder.ensureDebugId(creationTime, "creationTime");
        getDisplay().add(creationTime);

        lastModificationTime.setCaption(messages.lastModificationTime());
        DebugIdBuilder.ensureDebugId(lastModificationTime, "lastModificationTime");
        getDisplay().add(lastModificationTime);
    }

    private void bindToolBar()
    {
        addTool(ButtonCode.EDIT, commonMessages.edit(), ContentTemplateCommandCode.EDIT,
                new ContentTemplateCommandParam<>(refreshCallback),
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        addTool(ButtonCode.DELETE, commonMessages.delete(), ContentTemplateCommandCode.DELETE,
                new ContentTemplateCommandParam<>(removeCallback),
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.bind();
    }

    private void setPropertyValues()
    {
        code.setValue(contentTemplateDto.getUUID());
        settingsSetOnFormCreator.setValueOnCardProperty(contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.SETTINGS_SET), settingsSet);
        title.setValue(contentTemplateDto.getTitle());
        List<TitledClassFqn> classPath = contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.CLASS_PATH);
        if (null != classPath)
        {
            classFqn.setValue(classPath.stream()
                    .map(fqn -> formatters.linkToMetaClass(fqn, fqn.getTitle()).asString())
                    .collect(Collectors.joining(" / ")));
        }
        String contentTypeValue = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.CONTENT_TYPE);
        if (null != contentTypeValue)
        {
            contentType.setValue(contentTitles.content(contentTypeValue));
        }
        profiles.setValue(contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.PROFILES_VALUE));
        Date creationTimeValue = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.CREATION_TIME);
        creationTime.setValue(formatters.formatDateTime(creationTimeValue));
        Date lastModificationTimeValue = contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.LAST_MODIFICATION_TIME);
        lastModificationTime.setValue(formatters.formatDateTime(lastModificationTimeValue));

        ContentTemplate template = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.TEMPLATE);
        if (null != contentInfoPresenter && null != template)
        {
            contentInfoPresenter.setContentProperties(template.getTemplate(), contentTemplateDto);
        }
    }
}
