package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;

/**
 * Делегат изменения значения свойства "Тип объекта" элемента домашней страницы
 * {@link HomePage#REFERENCE_TAB_VALUE}
 * <AUTHOR>
 * @since 16.01.2023
 */
public class HomePageObjectCasesVCHDelegate implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.setProperty(ReferenceCode.REFERENCE_VALUE, null);
        context.getPropertyControllers().get(ReferenceCode.REFERENCE_VALUE).refresh();
    }
}