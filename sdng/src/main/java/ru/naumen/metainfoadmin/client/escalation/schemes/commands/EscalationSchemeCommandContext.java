package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.mvp.Display;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EscalationSchemeCommandContext
{
    private final EventBus localEventBus;
    private final Display display;

    public EscalationSchemeCommandContext(EventBus localEventBus, Display display)
    {
        this.localEventBus = localEventBus;
        this.display = display;
    }

    public Display getDisplay()
    {
        return display;
    }

    public EventBus getLocalEventBus()
    {
        return localEventBus;
    }
}