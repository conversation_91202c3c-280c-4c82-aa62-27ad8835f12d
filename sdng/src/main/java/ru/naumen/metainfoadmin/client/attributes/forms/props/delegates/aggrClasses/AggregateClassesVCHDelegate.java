package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.aggrClasses;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_OU_ATTR_GROUP;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_TEAM_ATTR_GROUP;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;

import java.util.Collection;
import java.util.Set;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Обработчик изменения значения свойства "Агрегировать классы"
 */
public class AggregateClassesVCHDelegate<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.setProperty(DEFAULT_VALUE, filterDefaultValue(context));
        context.getRefreshProcess()
                .startCustomProcess(Lists.newArrayList(DEFAULT_VALUE, COMPLEX_TEAM_ATTR_GROUP, COMPLEX_OU_ATTR_GROUP));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }

    protected static Object filterDefaultValue(PropertyContainerContext context)
    {
        TreeDtObject prevDefaultValue = (context.getPropertyValues().getProperty(DEFAULT_VALUE) instanceof Set)
                ? null
                : (TreeDtObject)context.getPropertyValues().getProperty(DEFAULT_VALUE);
        return getValueOrNull(prevDefaultValue, AggregateClassesUtils.getAggregateClasses(context));
    }

    private static Object getValueOrNull(TreeDtObject value, Collection<ClassFqn> aggregateClasses)
    {
        if (null == value || ObjectUtils.isEmpty(aggregateClasses))
        {
            return value;
        }

        ClassFqn valueFqn = value.getMetaClass();
        for (ClassFqn fqn : aggregateClasses)
        {
            if (isSuitable(valueFqn, fqn))
            {
                DtObject parent = value.getParent();
                if (parent != null && !parent.getMetaClass().isSameClass(Root.FQN))
                {
                    if (isSuitableParent(parent.getMetaClass(), aggregateClasses))
                    {
                        return value;
                    }
                }
                else
                {
                    return value;
                }
            }
        }
        return null;
    }

    private static boolean isSuitable(ClassFqn valueFqn, ClassFqn permittedFqn)
    {
        return permittedFqn.isCase() && permittedFqn.equals(valueFqn) || valueFqn.isCaseOf(permittedFqn);
    }

    private static boolean isSuitableParent(ClassFqn parent, Collection<ClassFqn> permittedTypes)
    {

        for (ClassFqn fqn : permittedTypes)
        {
            if (isSuitable(parent, fqn))
            {
                return true;
            }
        }

        return false;
    }
}
