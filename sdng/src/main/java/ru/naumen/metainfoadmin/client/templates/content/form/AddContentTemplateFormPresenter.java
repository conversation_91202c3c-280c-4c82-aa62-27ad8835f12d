package ru.naumen.metainfoadmin.client.templates.content.form;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Provider;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.MetainfoKeyCodeValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentFormType;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesPresenter;
import ru.naumen.metainfoadmin.client.templates.content.card.ContentTemplatePlace;

/**
 * Представление формы добавления шаблона контента.
 * <AUTHOR>
 * @since Mar 18, 2021
 */
public class AddContentTemplateFormPresenter extends ContentTemplateFormBasePresenter
{
    @Inject
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    private SelectListProperty<String, SelectItem> contentType;

    @Inject
    private TransliterationService transliterationService;
    @Inject
    private SecurityHelper securityHelper;
    @Inject
    private MetainfoKeyCodeValidator metainfoKeyCodeValidator;
    @Inject
    private AdminDialogMessages dialogMessages;
    @Inject
    private Provider<NotNullValidator<SelectItem>> notNullValidatorProvider;
    @Inject
    private PlaceController placeController;

    @Inject
    public AddContentTemplateFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public ContentFormType getContentFormType()
    {
        return ContentFormType.ADD_TEMPLATE;
    }

    @Override
    protected void afterSave(DtObject contentTemplateDto)
    {
        super.afterSave(contentTemplateDto);
        placeController.goTo(new ContentTemplatePlace(contentTemplateDto.getUUID()));
    }

    @Override
    protected void bindCommonProperties()
    {
        super.bindCommonProperties();
        contentType.setCaption(dialogMessages.contentType());
        DebugIdBuilder.ensureDebugId(contentType, "contentType");
        contentType.setValidationMarker(true);
        SingleSelectCellList<String> typeSelectList = contentType.getValueWidget();
        propertiesPresenterRegistry.getContentTypeOptions().forEach(typeSelectList::addItem);
        getDisplay().add(contentType);

        registerHandler(title.addValueChangeHandler(event ->
        {
            if (!StringUtilities.isEmpty(event.getValue()) && StringUtilities.isEmpty(code.getValue()))
            {
                String specialChars = securityHelper.hasVendorProfile()
                        ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                        : Constants.CODE_SPECIAL_CHARS;
                code.setValue(transliterationService.transliterateToCode(event.getValue(), ContentUtils.MAX_CODE_LENGTH,
                        specialChars));
            }
        }));
        registerHandler(contentType.addValueChangeHandler(event ->
                bindContentProperties(null == event.getValue() ? null : event.getValue().getUUID())));
        validator.validate(code, metainfoKeyCodeValidator);
        validator.validate(contentType, notNullValidatorProvider.get());
    }

    @Override
    protected void fillTemplate(ContentTemplate template)
    {
        template.setTemplate(contentPropertiesPresenter.createContent());
        template.setClassFqn(Employee.FQN);
        contentPropertiesPresenter.setContentProperties(template.getTemplate());
        template.getTemplate().setUuid(code.getValue());
        super.fillTemplate(template);
    }

    @Override
    protected int getContentPropertiesIndex()
    {
        return getDisplay().indexOf(contentType) + 1;
    }

    @Override
    protected ContentTemplate getCurrentTemplate()
    {
        return new ContentTemplate();
    }

    @Override
    protected String getFormTitle()
    {
        return messages.addTemplateForm();
    }

    @Override
    protected FlowContent getTemplateContent(ContentPropertiesPresenter<FlowContent> propertiesPresenter)
    {
        return propertiesPresenter.createContent();
    }

    @Override
    protected void saveTemplate(ContentTemplate template, AsyncCallback<DtObject> callback)
    {
        contentTemplateServiceAsync.addContentTemplate(template, callback);
    }
}
