package ru.naumen.metainfoadmin.client.escalation.actions;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfoadmin.client.eventaction.form.AddEventActionFormPresenter;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormGinModule.EventAttributesTree;
import ru.naumen.metainfoadmin.client.eventaction.form.SelectFqnsPropertyFactory;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeFactoryContext;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * <AUTHOR>
 * @since 01.08.2012
 */
public class AddEscalationEventActionFormPresenter extends AddEventActionFormPresenter
{
    @Inject
    public AddEscalationEventActionFormPresenter(
            @Named(EscalationActionsGinModule.ESCALATION_EVENT_ACTION_TREE) SelectFqnsPropertyFactory treeProvider,
            EventActionFormDisplay display,
            EventBus eventBus,
            DtoTreeFactory<Collection<DtObject>, EventAttributesTree, WithoutFolders,
                    EventAttributesTreeFactoryContext> attrTreeFactory,
            SettingsSetOnFormCreator settingsSetOnFormCreator)
    {
        super(treeProvider, display, eventBus, attrTreeFactory, settingsSetOnFormCreator);
    }

    @Override
    protected List<EventType> getAllowedEvents(List<MetaClassLite> metaClasses, Set<ClassFqn> selectedFqns,
            @Nullable String actionType)
    {
        return Lists.newArrayList(EventType.escalation);
    }

    @Override
    protected void initActionTypes()
    {
        initActionTypes(Sets.newHashSet(ActionType.IntegrationEventAction, ActionType.ChangeTrackingEventAction));
    }

    @Override
    protected void initPropertiesValues(EventActionWithScript eventAction, AsyncCallback<Void> callback)
    {
        ((ListBoxWithEmptyOptProperty)getEvent()).trySetObjValue(EventType.escalation.name());
        getEvent().setDisable();
        super.initPropertiesValues(eventAction, callback);
    }
}
