package ru.naumen.metainfoadmin.client.attributes.attrusage;

import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactory;
import ru.naumen.core.client.attr.presentation.factories.def.AttributeDefaultHtmlFactoryImpl;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInBreadCrumb;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInContent;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInDateTimeRestriction;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInEventAction;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInFastLink;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInGroup;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInHomePage;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInLinkToContentLeftMenuItem;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInLinkToContentTopMenuItem;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInMarker;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInMobileApp;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInStructuredObjectsViewItem;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInTimer;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInValueMap;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInWfProfiles;

/**
 * Реализация {@link AttributeDiffTypesHtmlFactories}
 * <AUTHOR>
 * @since 28.06.18
 */
@Singleton
public class AttributeDiffTypesHtmlFactoriesImpl implements AttributeDiffTypesHtmlFactories
{
    @SuppressWarnings("rawtypes")
    @Inject
    private Provider<AttributeDefaultHtmlFactoryImpl> defaultHtml;

    private final Map<Class<?>, AttributeHtmlFactory<?>> factories = new HashMap<>();

    @Inject
    //@formatter:off
    protected AttributeDiffTypesHtmlFactoriesImpl(
            AttributeUsageInGroupHtmlFactoryImpl attrGroupUsageAttrHtml,
            AttributeUsageInFastLinkHtmlFactoryImpl fastLinkUsageAttrHtml,
            AttributeUsageInWfProfilesHtmlFactoryImpl wfProfilesUsageAttrHtml,
            AttributeUsageInTimerHtmlFactoryImpl timerUsageAttrHtml,
            AttributeUsageInEventActionHtmlFactoryImpl eventActionUsageAttrHtml,
            AttributeUsageInBreadCrumbHtmlFactoryImpl breadCrumbUsageAttrHtml,
            AttributeUsageInMobileAppHtmlFactoryImpl mobileAppUsageAttrHtml,
            AttributeUsageInMarkerHtmlFactoryImpl markerUsageAttrHtml,
            AttributeUsageInContentHtmlFactoryImpl contentUsageAttrHtml,
            AttributeUsageInValueMapHtmlFactoryImpl valueMapUsageAttrHtml,
            AttributeUsageInDateTimeRestrictionHtmlFactoryImpl inRestrictionUsageAttrHtml,
            AttributeUsageInStructuredObjectsViewItemHtmlFactoryImpl structuredObjectsViewItemUsageAttrHtml,
            AttributeUsageInLinkToContentMenuItemHtmlFactoryImpl linkToContentItemItemUsageAttrHtml,
            AttributeUsageInHomePageHtmlFactoryImpl attributeUsageInHomePageHtml)
    //@formatter:on
    {
        register(AttributeUsageInGroup.class, attrGroupUsageAttrHtml);
        register(AttributeUsageInFastLink.class, fastLinkUsageAttrHtml);
        register(AttributeUsageInWfProfiles.class, wfProfilesUsageAttrHtml);
        register(AttributeUsageInTimer.class, timerUsageAttrHtml);
        register(AttributeUsageInEventAction.class, eventActionUsageAttrHtml);
        register(AttributeUsageInBreadCrumb.class, breadCrumbUsageAttrHtml);
        register(AttributeUsageInMobileApp.class, mobileAppUsageAttrHtml);
        register(AttributeUsageInMarker.class, markerUsageAttrHtml);
        register(AttributeUsageInContent.class, contentUsageAttrHtml);
        register(AttributeUsageInValueMap.class, valueMapUsageAttrHtml);
        register(AttributeUsageInDateTimeRestriction.class, inRestrictionUsageAttrHtml);
        register(AttributeUsageInStructuredObjectsViewItem.class, structuredObjectsViewItemUsageAttrHtml);
        register(AttributeUsageInLinkToContentLeftMenuItem.class, linkToContentItemItemUsageAttrHtml);
        register(AttributeUsageInLinkToContentTopMenuItem.class, linkToContentItemItemUsageAttrHtml);
        register(AttributeUsageInHomePage.class, attributeUsageInHomePageHtml);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> AttributeHtmlFactory<T> getFactory(Class<?> clazz)
    {
        if (factories.get(clazz) != null)
        {
            return (AttributeHtmlFactory<T>)factories.get(clazz);
        }
        else
        {
            return defaultHtml.get();
        }
    }

    @Override
    public <T> void register(Class<?> clazz, AttributeHtmlFactory<T> htmlFactoryProvider)
    {
        if (factories.containsKey(clazz))
        {
            throw new FxException("AttributeHtmlFactory with class '" + clazz + "' already registered");
        }
        factories.put(clazz, htmlFactoryProvider);
    }
}
