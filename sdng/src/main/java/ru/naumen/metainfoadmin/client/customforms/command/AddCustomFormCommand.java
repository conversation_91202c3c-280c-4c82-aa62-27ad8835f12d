package ru.naumen.metainfoadmin.client.customforms.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;
import ru.naumen.metainfoadmin.client.customforms.form.AddCustomFormFormPresenter;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
public class AddCustomFormCommand extends CustomFormPresenterCommandBase
{
    @Inject
    private final Provider<AddCustomFormFormPresenter> formProvider;

    @Inject
    public AddCustomFormCommand(@Assisted ContentCommandParam<CustomForm, CustomForm> param,
            Provider<AddCustomFormFormPresenter> formProvider)
    {
        super(param);
        this.formProvider = formProvider;
    }

    @Override
    protected CallbackPresenter<CustomForm, CustomForm> getPresenter(CustomForm value)
    {
        AddCustomFormFormPresenter presenter = formProvider.get();
        presenter.setMetaClass(this.metaClassUnderEdit);
        return presenter;
    }
}
