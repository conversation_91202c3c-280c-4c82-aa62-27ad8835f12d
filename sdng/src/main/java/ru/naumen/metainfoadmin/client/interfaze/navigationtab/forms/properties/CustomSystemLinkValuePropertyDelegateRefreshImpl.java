package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Логика обновления свойства "Начинается с url системы" для "Произвольной ссылки".
 * <AUTHOR>
 * @since 6 окт. 2021 г.
 */
public class CustomSystemLinkValuePropertyDelegateRefreshImpl implements
        PropertyDelegateRefresh<Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);
        context.getPropertyValues().setProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE, property.getValue());
        callback.onSuccess(MenuItemType.customLink.equals(type));
    }
}