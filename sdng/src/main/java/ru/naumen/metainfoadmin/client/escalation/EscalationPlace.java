package ru.naumen.metainfoadmin.client.escalation;

import java.util.Collections;
import java.util.List;

import ru.naumen.core.client.AbstractTabbedPlace;
import ru.naumen.core.client.activity.AbstractPlaceTokenizer;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * <AUTHOR>
 * @since 20.07.2012
 *
 */
public class EscalationPlace extends AbstractTabbedPlace
{

    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer extends AbstractPlaceTokenizer<EscalationPlace> implements
            PlaceTokenizer<EscalationPlace>
    {
        public Tokenizer()
        {
            super(Collections.<Converter<?>> emptyList());
        }

        @Override
        protected EscalationPlace getPlace(List<Object> split)
        {
            return new EscalationPlace();
        }
    }

    public static final String PLACE_PREFIX = "escalation";

    public EscalationPlace()
    {
    }

    public EscalationPlace(String tab)
    {
        setTab(tab);
    }

    @SuppressWarnings("unchecked")
    @Override
    public EscalationPlace cloneIt()
    {
        EscalationPlace clone = new EscalationPlace();
        clone.setParameters(cloneParameters());
        return clone;
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "EscalationPlace";
    }

}
