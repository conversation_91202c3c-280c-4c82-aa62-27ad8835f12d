package ru.naumen.metainfoadmin.client.templates.list;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Maps;

import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка шаблонов списка
 * <AUTHOR>
 * @since 06.04.2018
 */
public class ListTemplatesMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Inject
    private ListTemplatesMessages listTemplatesMessages;
    @Inject
    private ContentCreatorMessages contentCreatorMessages;

    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = Maps.newHashMap(super.createAttributes());
        attrs.put(ListTemplate.Attributes.ATTR_METACLASS.toString(),
                createLinkedClassesAttr(ListTemplate.Attributes.ATTR_METACLASS, contentCreatorMessages.objectClass()));
        attrs.put(ListTemplate.Attributes.ATTR_CASE.toString(),
                createCaseAttr(ListTemplate.Attributes.ATTR_CASE, contentCreatorMessages.objectsTypes()));
        attrs.put(ListTemplate.Attributes.ATTR_IS_USED.toString(),
                createBooleanAttr(ListTemplate.Attributes.ATTR_IS_USED, listTemplatesMessages.isUsedInList()));
        attrs.put(ListTemplate.Attributes.ATTR_CREATION_DATE.toString(),
                createDateTimeAttr(ListTemplate.Attributes.ATTR_CREATION_DATE, listTemplatesMessages.creationDate()));
        attrs.put(ListTemplate.Attributes.ATTR_LAST_MODIFIED_DATE.toString(), createDateTimeAttr(
                ListTemplate.Attributes.ATTR_LAST_MODIFIED_DATE, listTemplatesMessages.lastModifiedDate()));
        attrCodes.add(ListTemplate.Attributes.ATTR_METACLASS.toString());
        attrCodes.add(ListTemplate.Attributes.ATTR_CASE.toString());
        attrCodes.add(ListTemplate.Attributes.ATTR_IS_USED.toString());
        attrCodes.add(ListTemplate.Attributes.ATTR_CREATION_DATE.toString());
        attrCodes.add(ListTemplate.Attributes.ATTR_LAST_MODIFIED_DATE.toString());
        return attrs;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return ListTemplate.Attributes.ATTR_CODE;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return ListTemplate.Attributes.ATTR_TITLE;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return ListTemplate.FQN;
    }
}
