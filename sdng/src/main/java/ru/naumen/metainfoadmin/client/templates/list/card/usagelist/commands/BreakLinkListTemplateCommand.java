package ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.templates.list.dispatch.DeleteListTemplateUsagePointsAction;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;

/**
 * Команда разрыва связи шаблона списка
 * <AUTHOR>
 * @since 08.08.2018
 */
public class BreakLinkListTemplateCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    private ListTemplatesMessages listTemplatesMessages;
    @Inject
    private DispatchAsync dispatch;

    private List<DtObject> usagePoints;

    @Inject
    public BreakLinkListTemplateCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        usagePoints = values.stream()
                .filter(u -> !UI.LEFT_MENU_ITEM.equals(
                        u.getProperty(FakeMetaClassesConstants.UsageListTemplate.FORM_CODE)))
                .collect(Collectors.toList());
        if (usagePoints.size() == 1)
        {
            return listTemplatesMessages.usageListTemplateConfirmBreakLink(usagePoints.iterator().next().getTitle());
        }
        return listTemplatesMessages.usageListTemplateConfirmMassBreakLink();
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        List<String> usagePointsUuids = usagePoints.stream()
                .map(IUUIDIdentifiable::getUUID)
                .collect(Collectors.toList());
        String templateCode = ((ListTemplateUsageCommandParam)param).getTemplateCode();
        dispatch.execute(new DeleteListTemplateUsagePointsAction(templateCode, usagePointsUuids),
                CallbackDecorator.<EmptyResult, Void> nullReturn(param.getCallback()));
    }
}