package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT;

import java.util.ArrayList;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * <AUTHOR>
 * @since 26 сент. 2018 г.
 */
public class DateTimeRestrictionTypeVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{

    private static final ArrayList<String> DEPENDENT_ATTRIBUTES = Lists.newArrayList(DATE_TIME_RESTRICTION_SCRIPT,
            DATE_TIME_RESTRICTION_ATTRIBUTE, DATE_TIME_RESTRICTION_CONDITION, DATE_TIME_COMMON_RESTRICTIONS);

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(DEPENDENT_ATTRIBUTES);
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }

}
