package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * <AUTHOR>
 * @since 16 окт. 2013 г.
 */
@Singleton
public class ReferenceValuePropertyDelegateRefreshImpl implements
        PropertyDelegateRefresh<SelectItem, DtObjectSelectProperty>
{
    @Inject
    private ReferenceHelper referenceHelper;

    @Override
    public void refreshProperty(PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);
        /*
         * Пока оставляем возможность задавать ссылку только для элемента вида "Ссылка на карточку"
         * для всех видов меню
         * boolean isLeftMenuRef = MenuItemType.TYPES_WITH_REFERENCE.contains(type) &&
         *      context.getPropertyValues().hasProperty(MenuSettingsPropertyCode.PRESENTATION);
         */
        boolean isRef = MenuItemType.reference.equals(type);
        if (isRef)
        {
            DtObject typeOfCard = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
            RelationsAttrTreeObject attrThee = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
            referenceHelper.fillReferenceValueProperty(ReferenceHelper.getObjectClassFqnByCardType(typeOfCard,
                    attrThee), property, context, attrThee);
        }
        callback.onSuccess(isRef);
        if (!isRef)
        {
            context.getPropertyControllers().get(ReferenceCode.REFERENCE_VALUE).unbindValidators();
        }
    }
}
