package ru.naumen.metainfoadmin.client.attributes.forms.usage;

import java.util.ArrayList;

import com.google.inject.Singleton;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.advlist.AdvlistConstants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageAttr;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.AdvlistUIContext;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.advlist.FeatureCodes;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка объектов {@link UsageAttr} для формы "Используется в настройках"
 * <AUTHOR>
 * @since 15 Jun 18
 */
@Singleton
public class AttributeUsageAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Override
    public ListPresenter<CustomList> create(Context context)
    {
        ListPresenter<CustomList> list = advlistPresenterProvider.get();
        MetaClass metaClass = getMetaClassInt();
        CustomList objectList = createContent(context, metaClass);
        list.init(objectList, new AdvlistUIContext(context, null, false, null, list.getListComponents()));
        return list;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        return null;
    }

    @Override
    protected CustomList createContent(Context context, MetaClass metaclass)
    {
        CustomList content = super.createContent(context, metaclass);
        content.setDefaultPageSize(AdvlistConstants.DEFAULT_PAGE_SIZE);
        content.setFeatures(FeatureCodes.PAGING);
        AttributeFqn attrFqn = context.getContextProperty(Constants.ATTRIBUTE_FQN);
        DtoCriteria criteria = new DtoCriteria(metaclass.getFqn());
        criteria.addFilters(Filters.eq(Constants.ATTRIBUTE_FQN, attrFqn));
        content.setCriteria(criteria);
        return content;
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        return null;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return UsageAttr.FQN;
    }
}