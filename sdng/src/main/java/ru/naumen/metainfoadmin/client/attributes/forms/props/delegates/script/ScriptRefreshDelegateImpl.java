package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.script;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.SYSTEM_COMPUTABLE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Обработчик обновления значения свойства "Скрипт" 
 * <AUTHOR>
 * @since 05.07.2012
 *
 */
public class ScriptRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ScriptComponentEditProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean computable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean systemComputable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(SYSTEM_COMPUTABLE));
        callback.onSuccess(computable && !systemComputable);
    }
}
