package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 15.08.2012
 *
 */
public class FilterByScriptVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(
                Arrays.asList(AttributeFormPropertyCode.SCRIPT_FOR_FILTRATION, AttributeFormPropertyCode.EDIT_PRS));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
