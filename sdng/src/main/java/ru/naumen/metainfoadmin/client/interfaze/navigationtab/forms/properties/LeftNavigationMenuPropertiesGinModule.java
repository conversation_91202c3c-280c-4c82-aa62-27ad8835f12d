package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAfterHierarchyAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAfterHierarchyAttributeControllerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAttributeControllerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentBeforeHierarchyAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentBeforeHierarchyAttributeControllerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectAttributeControllerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.ListTemplateSettingsTreePropertyControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.ListTemplateSettingsTreePropertyControllerImpl;

/**
 * Модуль подключения контроллеров свойств типа "дерево" на форме добавления элемента левого меню "Ссылка на контент"
 *
 * <AUTHOR>
 * @since 22.10.2020
 */
public class LeftNavigationMenuPropertiesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bindCommonTree();
    }

    private void bindCommonTree()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, ListTemplateSettingsTreePropertyControllerImpl.class)
                .build(new TypeLiteral<ListTemplateSettingsTreePropertyControllerFactory>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, AddButtonSettingsTreePropertyControllerImpl.class)
                .build(new TypeLiteral<AddButtonLeftMenuTreePropertyControllerFactory>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, LinkToContentAttributeControllerImpl.class)
                .build(new TypeLiteral<LinkToContentAttributeControllerFactory>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, LinkToContentLinkObjectAttributeControllerImpl.class)
                .build(new TypeLiteral<LinkToContentLinkObjectAttributeControllerFactory>(){}));


        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, LinkToContentBeforeHierarchyAttributeControllerImpl.class)
                .build(new TypeLiteral<LinkToContentBeforeHierarchyAttributeControllerFactory>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, LinkToContentAfterHierarchyAttributeControllerImpl.class)
                .build(new TypeLiteral<LinkToContentAfterHierarchyAttributeControllerFactory>(){}));
        //@formatter:on
    }
}
