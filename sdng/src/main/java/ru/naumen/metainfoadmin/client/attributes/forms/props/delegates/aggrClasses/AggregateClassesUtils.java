package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.aggrClasses;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.AGGREGATE_CLASSES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.CODE;

import java.util.ArrayList;
import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 *
 * Утилитарные методы для работы со свойствами Агрегирующих атрибутов
 * на форме добавления/редактирования атрибутов
 * <AUTHOR>
 * @since 06.03.2020
 */
@Singleton
public class AggregateClassesUtils
{
    /**
     * Получить список агрегируемых классов
     */
    public static Collection<ClassFqn> getAggregateClasses(@Nonnull PropertyContainerContext context)
    {
        Collection<MetaClassLite> metaClasses = context.getPropertyValues().getProperty(AGGREGATE_CLASSES);
        if (metaClasses == null)
        {
            MetaClass metaClass = context.getContextValues().getProperty(METAINFO);
            Attribute attribute = metaClass.getAttribute(context.getPropertyValues().<String> getProperty(CODE));
            if (attribute == null)
            {
                return new ArrayList<>();
            }
            AggregateAttributeType attrType = attribute.getType().cast();
            return attrType.getAttributes().stream()
                    .map(AttributeDescription::getReferenceMetaClass)
                    .collect(Collectors.toList());
        }
        else
        {
            return metaClasses.stream()
                    .map(MetaClassLite::getFqn)
                    .collect(Collectors.toList());
        }
    }
}
