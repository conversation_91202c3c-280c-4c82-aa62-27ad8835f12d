package ru.naumen.metainfoadmin.client.templates.content.columns;

import com.google.gwt.safehtml.shared.SafeHtml;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Фабрика для генерации основных параметров шаблона.
 * @param <C> тип контента
 * <AUTHOR>
 * @since Apr 05, 2021
 */
public interface ContentTemplateSummaryFactory<C extends Content>
{
    SafeHtml createSummary(DtObject contentTemplateDto, C templateContent);
}
