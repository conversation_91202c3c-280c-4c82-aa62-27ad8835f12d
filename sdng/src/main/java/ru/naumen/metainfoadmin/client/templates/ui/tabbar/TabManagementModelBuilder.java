package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.reference.TabBarReference;
import ru.naumen.metainfo.shared.ui.reference.TabReference;

/**
 * Построитель модели управления вкладками шаблона.
 * <AUTHOR>
 * @since Aug 01, 2021
 */
@Singleton
public class TabManagementModelBuilder
{
    private final MetainfoUtils metainfoUtils;

    @Inject
    public TabManagementModelBuilder(MetainfoUtils metainfoUtils)
    {
        this.metainfoUtils = metainfoUtils;
    }

    public List<TabModel> build(TabBar tabBar, TabBarReference tabBarReference)
    {
        List<TabModel> tabs = new ArrayList<>();
        tabs.add(new TabModel());

        Map<String, Tab> contentTabMap = new HashMap<>();
        List<Tab> contentTabs = new ArrayList<>(tabBar.getTab());
        tabBar.getTab().forEach(tab -> contentTabMap.put(tab.getUuid(), tab));

        for (TabReference tabReference : tabBarReference.getTabs())
        {
            Tab original = contentTabMap.get(tabReference.getUuid());
            if (null == original)
            {
                continue;
            }
            TabModel model = new TabModel();
            model.setTitle(metainfoUtils.getLocalizedValue(tabReference.getCaption()));
            model.setEnabled(true);
            model.setCode(tabReference.getUuid());
            model.setOriginalTitle(metainfoUtils.getLocalizedValue(original.getCaption()));
            contentTabs.remove(original);
            tabs.add(model);
        }
        for (Tab tab : contentTabs)
        {
            TabModel model = new TabModel();
            model.setTitle(metainfoUtils.getLocalizedValue(tab.getCaption()));
            model.setEnabled(false);
            model.setCode(tab.getUuid());
            model.setOriginalTitle(model.getTitle());
            tabs.add(model);
        }
        return tabs;
    }
}
