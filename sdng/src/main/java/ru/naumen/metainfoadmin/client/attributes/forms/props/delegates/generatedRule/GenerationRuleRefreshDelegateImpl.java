package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 10 дек. 2013 г.
 */
public class GenerationRuleRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, String, FootedTextBoxProperty>
{
    @Inject
    private CommonMessages messages;

    @Override
    public void refreshProperty(PropertyContainerContext context, FootedTextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        property.setCaption(getCaption(context.getPropertyValues().<String> getProperty(
                AttributeFormPropertyCode.ATTR_TYPE)));
        Boolean useGenerationRule = context.getPropertyValues().getProperty(AttributeFormPropertyCode.USE_GEN_RULE);
        callback.onSuccess(useGenerationRule);
    }

    private String getCaption(String attrTypeCode)
    {
        if (IntegerAttributeType.CODE.equals(attrTypeCode))
        {
            return messages.numberRule();
        }
        return messages.nameRule();
    }
}