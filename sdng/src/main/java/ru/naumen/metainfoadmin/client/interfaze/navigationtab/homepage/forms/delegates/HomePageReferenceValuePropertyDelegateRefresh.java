package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления свойства "Вкладка карточки" элемента домашней страницы
 * {@link ReferenceCode#REFERENCE_VALUE}
 *
 * <AUTHOR>
 * @since 16.01.2023
 */
public class HomePageReferenceValuePropertyDelegateRefresh implements
        PropertyDelegateRefresh<SelectItem, DtObjectSelectProperty>
{
    private final ReferenceHelper referenceHelper;

    @Inject
    public HomePageReferenceValuePropertyDelegateRefresh(ReferenceHelper referenceHelper)
    {
        this.referenceHelper = referenceHelper;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(HomePage.TYPE);
        boolean isRef = HomePageType.REFERENCE.name().equals(typeStr);
        if (isRef)
        {
            String referenceCardType = context.getPropertyValues().getProperty(HomePage.REFERENCE_CARD_TYPE, null);
            RelationsAttrTreeObject attrTree = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
            ClassFqn fqn =
                    ReferenceHelper.getObjectClassFqnByCardType(new SimpleDtObject(referenceCardType, ""), attrTree);
            referenceHelper.fillReferenceValueProperty(fqn, property, context, null);
        }
        else
        {
            context.getPropertyControllers().get(ReferenceCode.REFERENCE_VALUE).unbindValidators();
        }
        callback.onSuccess(isRef);
    }
}
