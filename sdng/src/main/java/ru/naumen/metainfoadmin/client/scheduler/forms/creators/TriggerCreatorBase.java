package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Базовый класс для пользовательских задач планировщика
 * <AUTHOR>
 * @since 26.05.2022
 */
public abstract class TriggerCreatorBase extends FormPropertiesCreator
{
    @Inject
    protected SharedSettingsClientService sharedSettings;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> randomizeDelayOn;
    protected Trigger trigger;

    protected void addRandomizeDelay(String caption)
    {
        if (sharedSettings.useRandomizeDelay())
        {
            randomizeDelayOn.setCaption(caption);
            add(randomizeDelayOn);
            randomizeDelayOn.setValue(trigger != null && trigger.isRandomizeDelayOn());
            DebugIdBuilder.ensureDebugId(randomizeDelayOn, "randomizeDelay");
        }
    }
}