package ru.naumen.metainfoadmin.client.templates.list;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.list.commands.DelListTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.list.commands.EditListTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.list.commands.EditParametersListTemplateCommand;

/**
 * Инициализатор команд для шаблонов списков
 * <AUTHOR>
 * @since 19.04.2018
 */
@Singleton
public class ListTemplatesCommandFactoryInitializer
{
    @Inject
    public ListTemplatesCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<EditListTemplateCommand, CommandParam<DtObject, DtObject>> editProvider,
            CommandProvider<DelListTemplateCommand, CommandParam<Collection<DtObject>, Void>> deleteProvider,
            CommandProvider<EditParametersListTemplateCommand, CommandParam<DtObject, DtObject>> editParametersProvider)
    {
        factory.register(ListTemplatesCommandCode.EDIT, editProvider);
        factory.register(ListTemplatesCommandCode.DELETE, deleteProvider);
        factory.register(ListTemplatesCommandCode.EDIT_PARAMETERS, editParametersProvider);
    }
}
