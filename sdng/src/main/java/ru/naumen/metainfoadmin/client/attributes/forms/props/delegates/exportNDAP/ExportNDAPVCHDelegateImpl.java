package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EXPORT_NDAP;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * <AUTHOR>
 * @since 10.05.2016
 */
public class ExportNDAPVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean exportNDAP = context.getPropertyValues().getProperty(EXPORT_NDAP);
        if (exportNDAP)
        {
            context.setProperty(COMPUTABLE, false);
        }
        else
        {
            context.setProperty(RELATED_ATTRS_TO_EXPORT, null);
        }
        context.getRefreshProcess().startCustomProcess(Arrays.asList(COMPUTABLE, RELATED_ATTRS_TO_EXPORT));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
