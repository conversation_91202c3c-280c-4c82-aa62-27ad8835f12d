package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;

/**
 * <AUTHOR>
 * @since 31.05.2012
 */
public interface AttributeFormPropertyDelegateBind<F extends ObjectForm, T, P extends Property<T>> extends
        PropertyDelegateBind<T, P>
{
}
