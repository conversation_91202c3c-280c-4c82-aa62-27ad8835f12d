package ru.naumen.metainfoadmin.client.attributes.attrusage;

import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.attrusage.AttributeUsagePlace;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationLeftMenuItemPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationMenuItemPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTopMenuItemPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInLinkToContentLeftMenuItem;

/**
 * Фабрика генерации ссылки на элемент левого/верхнего меню типа "Ссылка на контент", "Ссылка на карточку"
 * Предполагается использование в блоке "Места использования атрибута".
 *
 * <AUTHOR>
 * @since 15.12.2020
 */
public class AttributeUsageInLinkToContentMenuItemHtmlFactoryImpl extends
        AttributeHtmlFactoryImpl<AttributeUsagePlace>
{
    @Inject
    private Formatters formatters;
    @Inject
    private PlaceHistoryMapper historyMapper;
    @Inject
    private NavigationSettingsMessages navMessages;

    @Override
    public SafeHtml create(@Nullable PresentationContext context, AttributeUsagePlace usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLink(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLink(AttributeUsagePlace usage)
    {
        String title = Objects.requireNonNull(usage.getTitle());
        String code = Objects.requireNonNull(usage.getCode());
        NavigationMenuItemPlace<?> place = usage instanceof AttributeUsageInLinkToContentLeftMenuItem ?
                new NavigationLeftMenuItemPlace(code) : new NavigationTopMenuItemPlace(code);
        return new Hyperlink(navMessages.menuElementWithTitle(title),
                StringUtilities.getHrefByToken(historyMapper.getToken(place)));
    }
}