package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemFormatting;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;

/**
 * Делегат биндинга свойства "Форматирование" элемента левого меню
 * <AUTHOR>
 * @since 27.06.2020
 */
public class LeftMenuFormattingBindDelegateImpl extends PropertyDelegateBindImpl<SelectItem, ListBoxProperty>
{
    @Inject
    @Named(NavigationTabSettingsGinModule.MENU_FORMATTING_TITLES)
    private Map<MenuItemFormatting, String> menuTitles;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        SingleSelectCellList<?> selList = property.getValueWidget();
        for (MenuItemFormatting pres : MenuItemFormatting.values())
        {
            selList.addItem(menuTitles.get(pres), pres.name());
        }
        super.bindProperty(context, property, callback);
    }
}