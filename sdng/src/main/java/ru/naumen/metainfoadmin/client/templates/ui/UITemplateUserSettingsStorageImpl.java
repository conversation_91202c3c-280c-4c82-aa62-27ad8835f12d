package ru.naumen.metainfoadmin.client.templates.ui;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import com.google.gwt.storage.client.Storage;

import ru.naumen.core.client.StorageType;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Реализация хранилища персональных параметров технолога при настройке шаблона.
 * <AUTHOR>
 * @since Aug 04, 2021
 */
@Singleton
public class UITemplateUserSettingsStorageImpl implements UITemplateUserSettingsStorage
{
    private static final String CURRENT_TEMPLATE_PREFIX = "currentTemplate:";

    private final Storage storage;

    @Inject
    public UITemplateUserSettingsStorageImpl(@Named(StorageType.LocalStorage) Storage storage)
    {
        this.storage = storage;
    }

    @Override
    @Nullable
    public String getCurrentTemplate(UIContext context)
    {
        return storage.getItem(getStorageKey(context));
    }

    @Override
    public void setCurrentTemplate(UIContext context, @Nullable String templateCode)
    {
        if (null == templateCode)
        {
            storage.removeItem(getStorageKey(context));
        }
        else
        {
            storage.setItem(getStorageKey(context), templateCode);
        }
    }

    private static String getStorageKey(UIContext context)
    {
        return CURRENT_TEMPLATE_PREFIX + context.getMetainfo().getFqn() + ':' + context.getCode();
    }
}
