package ru.naumen.metainfoadmin.client.templates.content.command;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.SystemCatalogIconCodes;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateMessages;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateServiceAsync;

/**
 * Команда массового удаления шаблонов контентов.
 * <AUTHOR>
 * @since Mar 25, 2021
 */
public class DeleteContentTemplatesCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    private final ContentTemplateServiceAsync contentTemplateServiceAsync;
    private final ContentTemplateMessages messages;
    private final CommonMessages commonMessages;

    @Inject
    public DeleteContentTemplatesCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs,
            ContentTemplateServiceAsync contentTemplateServiceAsync,
            ContentTemplateMessages messages, CommonMessages commonMessages)
    {
        super(param, dialogs);
        this.contentTemplateServiceAsync = contentTemplateServiceAsync;
        this.messages = messages;
        this.commonMessages = commonMessages;
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> value)
    {
        return messages.confirmMassDeleteTemplate();
    }

    @Override
    protected String getDialogTitle()
    {
        return commonMessages.confirmDelete();
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        List<String> codes = param.getValue().stream().map(DtObject::getUUID).collect(Collectors.toList());
        contentTemplateServiceAsync.deleteContentTemplates(codes, param.getCallbackSafe());
    }

    @Override
    protected String getIconCode()
    {
        return SystemCatalogIconCodes.DELETE_ICON;
    }
}
