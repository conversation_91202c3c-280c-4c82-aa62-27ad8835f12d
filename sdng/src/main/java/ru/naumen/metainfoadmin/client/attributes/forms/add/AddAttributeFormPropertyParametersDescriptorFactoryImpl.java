package ru.naumen.metainfoadmin.client.attributes.forms.add;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.CODE;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyParametersDescriptorFactoryImpl;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class AddAttributeFormPropertyParametersDescriptorFactoryImpl<F extends ObjectFormAdd>
        extends AttributeFormPropertyParametersDescriptorFactoryImpl<F>
{
    @Override
    protected void build()
    {
        super.build();
        registerOrModifyProperty(CODE, cmessages.code(), true, "code", 3, true, true);
    }
}
