package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.ArrayList;
import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;

/**
 * Делегат отрисовки для виджета {@link ReferenceCode#OBJECT_CASES}
 * <AUTHOR>
 * @since 12.03.2022
 */
public class ObjectCasesReferenceDelegateBindImpl extends
        PropertyDelegateBindImpl<Collection<SelectItem>, MultiSelectBoxProperty>
{
    @Override
    public void bindProperty(final PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Void> callback)
    {
        Collection<String> selectedCases = context.getPropertyValues()
                .getProperty(ReferenceCode.OBJECT_CASES, new ArrayList<>());
        property.trySetObjValue(selectedCases);
        context.setProperty(ReferenceCode.OBJECT_CASES, selectedCases);
        callback.onSuccess(null);
    }
}