package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 28.05.2012
 */
public interface GetPermittedRelatedTypesCallbackFactory
{
    AsyncCallback<BatchResult> create(PropertyContainerContext context, AsyncCallback<Boolean> callback);
}
