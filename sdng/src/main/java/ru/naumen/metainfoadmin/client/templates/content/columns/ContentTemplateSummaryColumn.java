package ru.naumen.metainfoadmin.client.templates.content.columns;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Столбец с основными параметрами шаблона.
 * <AUTHOR>
 * @since Apr 05, 2021
 */
public class ContentTemplateSummaryColumn extends Column<DtObject, SafeHtml>
{
    private final ContentTemplateSummaryService summaryService;

    @Inject
    public ContentTemplateSummaryColumn(ContentTemplateSummaryService summaryService)
    {
        super(new ClickableSafeHtmlTextCell());
        this.summaryService = summaryService;
    }

    @Override
    public SafeHtml getValue(DtObject object)
    {
        return summaryService.generateSummary(object);
    }
}
