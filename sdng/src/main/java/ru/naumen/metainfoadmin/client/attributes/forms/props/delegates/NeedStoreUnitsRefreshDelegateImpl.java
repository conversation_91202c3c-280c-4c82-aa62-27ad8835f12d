package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления поля "Запоминать выбранные единицы измерения"
 * <AUTHOR>
 * @since 29.03.2017
 */
public class NeedStoreUnitsRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, <PERSON><PERSON><PERSON>, BooleanCheckBoxInfoProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxInfoProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        callback.onSuccess(DateTimeIntervalAttributeType.CODE.equals(attrType));
    }
}
