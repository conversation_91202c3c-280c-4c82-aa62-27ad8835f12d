package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import jakarta.inject.Singleton;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Локализованные названия типов ограничений для атибутов дата/время
 * <AUTHOR>
 * @since 4 окт. 2018 г.
 */
@DefaultLocale("ru")
@Singleton
public interface DateTimeRestrictionMessages extends Messages
{

    @Description("Задать зависимость от атрибута")
    String byAttributeTitle();

    @Description("Задать зависимость от параметра")
    String byParameterTitle();

    @Description("Ограничение скриптом")
    String byScriptTitle();

    @Description("Без ограничений")
    String defaultTitle();

    @Description("Больше или равно")
    String gteTitle();

    @Description("Больше")
    String gtTitle();

    @Description("Меньше или равно")
    String lteTitle();

    @Description("Меньше")
    String ltTitle();

}
