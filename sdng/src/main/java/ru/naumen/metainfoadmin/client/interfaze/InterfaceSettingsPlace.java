package ru.naumen.metainfoadmin.client.interfaze;

import java.util.Collections;
import java.util.List;

import ru.naumen.core.client.AbstractTabbedPlace;
import ru.naumen.core.client.activity.AbstractPlaceTokenizer;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * {@link Place} окна настроек интерфейса.
 *
 * <AUTHOR>
 * @since 11.02.2013
 */
public class InterfaceSettingsPlace extends AbstractTabbedPlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer extends AbstractPlaceTokenizer<InterfaceSettingsPlace> implements
            PlaceTokenizer<InterfaceSettingsPlace>
    {
        public Tokenizer()
        {
            super(Collections.<Converter<?>> emptyList());
        }

        @Override
        protected InterfaceSettingsPlace getPlace(List<Object> split)
        {
            return new InterfaceSettingsPlace();
        }

    }

    public static final String PLACE_PREFIX = "interface";
    public static final InterfaceSettingsPlace INSTANCE = new InterfaceSettingsPlace();

    public InterfaceSettingsPlace()
    {
    }

    public InterfaceSettingsPlace(String tab)
    {
        setTab(tab);
    }

    @SuppressWarnings("unchecked")
    @Override
    public InterfaceSettingsPlace cloneIt()
    {
        InterfaceSettingsPlace clone = new InterfaceSettingsPlace();
        clone.setParameters(cloneParameters());
        return clone;
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "InterfacePlace";
    }
}
