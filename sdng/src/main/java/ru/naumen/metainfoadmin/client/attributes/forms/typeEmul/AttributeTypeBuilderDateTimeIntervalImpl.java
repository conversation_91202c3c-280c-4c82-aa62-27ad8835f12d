package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * <AUTHOR>
 * @since 30.03.2017
 */
@Singleton
public class AttributeTypeBuilderDateTimeIntervalImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS,
                propertyValues.getProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS));
        type.setProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS,
                propertyValues.getProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        propertyValues.setProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS,
                type.getProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS));
        propertyValues.setProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS,
                type.getProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS));
    }
}