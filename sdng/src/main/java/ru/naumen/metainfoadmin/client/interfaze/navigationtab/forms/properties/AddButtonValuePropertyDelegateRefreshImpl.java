package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * <AUTHOR>
 * @since 16 окт. 2013 г.
 */
@Singleton
public class AddButtonValuePropertyDelegateRefreshImpl implements
        PropertyDelegateRefresh<Collection<DtObject>, Property<Collection<DtObject>>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, Property<Collection<DtObject>> property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);
        callback.onSuccess(MenuItemType.addButton.equals(type));
    }
}
