package ru.naumen.metainfoadmin.client.templates.list.card;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.components.block.TitledBlockDisplay;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.UsageListTemplateAdvlistFactory;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.UsageListTemplateCommandCode;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands.ListTemplateUsageCommandParam;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionHandler;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Блок "Места использования" на карточке.
 * <AUTHOR>
 * @since 06.08.2018
 */
public class ListTemplateUsagePresenter extends BasicPresenter<TitledBlockDisplay> implements ObjectListActionHandler
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    private UsageListTemplateAdvlistFactory listFactory;
    @Inject
    private CommandFactory commandFactory;

    private DtObject template;
    private ListPresenter<CustomList> listPresenter;

    private OnStartCallback<Void> refreshCallback = new SafeOnStartBasicCallback<Void>(getDisplay())
    {
        @Override
        protected void handleSuccess(Void value)
        {
            listPresenter.refreshDisplay();
            listPresenter.getListComponents().getSelectionModel().clear();
        }
    };

    @Inject
    public ListTemplateUsagePresenter(TitledBlockDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        display.asWidget().ensureDebugId("usage");
    }

    @Override
    public void init(ListComponents components)
    {
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public void onObjectListAction(ObjectListActionEvent event)
    {
        if (UsageListTemplateCommandCode.COMMANDS_IN_LIST.contains(event.getAction()))
        {
            CommandParam commandParam = new ListTemplateUsageCommandParam(template.getUUID(), event.getValues(),
                    refreshCallback);
            BaseCommand<?, ?> command = commandFactory.create(event.getAction(), commandParam);
            command.execute(commandParam);
        }
    }

    public void setTemplate(DtObject template)
    {
        this.template = template;
        refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(cmessages.usagePlaces());

        BasicUIContext context = new BasicUIContext(null, null);
        context.setObject(template);
        listPresenter = listFactory.create(context);
        registerChildPresenter(listPresenter, true);
        getDisplay().setControlledWidget(listPresenter.getDisplay());
        listPresenter.refreshDisplay();
        registerHandler(eventBus.addHandler(ObjectListActionEvent.getType(), this));
    }
}
