package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.stream.Stream;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.menu.MenuItemTypeOfCard;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Делегат отрисовки для виджета {@link MenuItemPropertyCode#TYPE_OF_CARD}
 *
 * <AUTHOR>
 * @since 12.03.2022
 */
public class TypeOfCardReferenceValueBindDelegateImpl extends PropertyDelegateBindImpl<SelectItem,
        DtObjectSelectProperty>
{
    @Inject
    private NavigationSettingsMessages messages;

    @Override
    public void bindProperty(final PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Void> callback)
    {
        final SingleSelectCellList<DtObject> selectList = property.getValueWidget();
        selectList.setHasEmptyOption(false);
        Stream.of(MenuItemTypeOfCard.values()).forEach(v ->
        {
            SimpleDtObject dto = new SimpleDtObject(v.name(), messages.contentMenuItemObject(v.name()));
            selectList.addItem(dto);
        });
        String currentObject = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
        DtObject value = StringUtilities.isEmpty(currentObject) ?
                selectList.getItem(0) :
                selectList.getItem(currentObject);
        property.trySetObjValue(value);
        context.setProperty(MenuItemPropertyCode.TYPE_OF_CARD, value);
        callback.onSuccess(null);
    }
}
