package ru.naumen.metainfoadmin.client.attributes;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import ru.naumen.core.client.components.block.TitledBlockWithToolDisplayImpl;
import ru.naumen.core.client.widgets.AdminPropertyList;
import ru.naumen.core.client.widgets.PropertyList;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Реализация {@link InfoDisplay}
 *
 * <AUTHOR>
 * @since 14.10.2010
 *
 */
public class InfoDisplayImpl extends TitledBlockWithToolDisplayImpl implements InfoDisplay
{
    private final PropertyList properties;

    @Inject
    public InfoDisplayImpl()
    {
        addControlledWidget(this.properties = new AdminPropertyList());
        properties.ensureDebugId("Info");
        WidgetResources.INSTANCE.additional().ensureInjected();
    }

    @Override
    public <T> PropertyRegistration<T> add(@Nullable Property<T> property)
    {
        return properties.add(property);
    }

    @Override
    public <T> PropertyRegistration<T> add(@Nullable Property<T> property, @Nullable String debugId)
    {
        return properties.add(property, debugId);
    }

    @Override
    public <T> PropertyRegistration<T> addProperty(@Nullable Property<T> property, int index)
    {
        return properties.addProperty(property, index);
    }

    @Override
    public <T, R> PropertyRegistration<T> addPropertyAfter(Property<T> property, PropertyRegistration<R> after)
    {
        return properties.addPropertyAfter(property, after);
    }

    @Override
    public void clearProperties()
    {
        properties.clearProperties();
    }

    @Override
    public int getPropertiesCount()
    {
        return properties.getPropertiesCount();
    }

    @Override
    public String getTitle()
    {
        return getCaption();
    }

    @Override
    public void hideTitle()
    {
        setCaptionVisible(false);
    }

    @Override
    public <T> PropertyRegistration<T> setProperty(Property<T> property, int index)
    {
        return properties.setProperty(property, index);
    }

    @Override
    public void setTitle(String title)
    {
        setCaption(title);
    }

    @Override
    public void showTitle()
    {
        setCaptionVisible(true);
    }
}
