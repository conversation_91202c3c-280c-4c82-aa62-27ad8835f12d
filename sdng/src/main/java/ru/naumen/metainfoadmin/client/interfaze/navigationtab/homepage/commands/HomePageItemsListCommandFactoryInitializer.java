package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageCommandParam;

/**
 * Инициализатор команд для списка элементов домашней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
@Singleton
public class HomePageItemsListCommandFactoryInitializer
{
    @Inject
    public HomePageItemsListCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<AddHomePageItemCommand, HomePageCommandParam> addProvider,
            CommandProvider<EditHomePageItemCommand, HomePageCommandParam> editProvider,
            CommandProvider<DeleteHomePageItemCommand, HomePageCommandParam> deleteProvider,
            CommandProvider<MoveHomePageItemUpCommand, HomePageCommandParam> moveItemUpProvider,
            CommandProvider<MoveHomePageItemDownCommand, HomePageCommandParam> moveItemDownProvider)
    {
        factory.register(HomePageItemsListCommandCode.ADD, addProvider);
        factory.register(HomePageItemsListCommandCode.EDIT, editProvider);
        factory.register(HomePageItemsListCommandCode.DELETE, deleteProvider);
        factory.register(HomePageItemsListCommandCode.MOVE_ITEM_UP, moveItemUpProvider);
        factory.register(HomePageItemsListCommandCode.MOVE_ITEM_DOWN, moveItemDownProvider);
    }
}
