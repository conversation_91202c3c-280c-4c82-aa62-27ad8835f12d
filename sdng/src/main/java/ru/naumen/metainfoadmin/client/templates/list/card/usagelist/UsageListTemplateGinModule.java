package ru.naumen.metainfoadmin.client.templates.list.card.usagelist;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands.ApplyListTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands.BreakLinkListTemplateCommand;

/**
 * <AUTHOR>
 * @since 08.08.2018
 */
public class UsageListTemplateGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(UsageListTemplateCommandFactoryInitializer.class).asEagerSingleton();
        configureCommands();
    }

    protected void configureCommands()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, BreakLinkListTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<BreakLinkListTemplateCommand, CommandParam<Collection<DtObject>, Void>>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ApplyListTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<ApplyListTemplateCommand, CommandParam<Collection<DtObject>, Void>>>(){}));
        //@formatter:on
    }
}
