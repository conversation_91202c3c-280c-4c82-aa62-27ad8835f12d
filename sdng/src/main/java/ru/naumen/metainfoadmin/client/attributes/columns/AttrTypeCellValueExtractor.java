package ru.naumen.metainfoadmin.client.attributes.columns;

import com.google.gwt.dom.client.Element;

import ru.naumen.admin.client.widgets.AdminWidgetResources;

/**
 * Извлекает тип атрибута (системный/пользовательский)
 * <AUTHOR>
 * @since 8 окт. 2018 г.
 *
 */
public class AttrTypeCellValueExtractor extends ByTagAndClassNameCellValueExtractor
{
    private boolean isSystemFirst;

    public AttrTypeCellValueExtractor(boolean isSystemFirst)
    {
        this.isSystemFirst = isSystemFirst;
    }

    @Override
    String getTagName()
    {
        return TAG_NAME_DIV;
    }

    @Override
    String getClassName()
    {
        return AdminWidgetResources.INSTANCE.attributeList().hardcodedAttribute();
    }

    @Override
    public String extractValue(Element td)
    {
        String result = super.extractValue(td);
        //Если нашли - атрибут системный, не нашли - пользовательский.
        boolean isSystem = result != null;
        return (isSystem && isSystemFirst || !isSystem && !isSystemFirst) ? "0" : "1";
    }
}
