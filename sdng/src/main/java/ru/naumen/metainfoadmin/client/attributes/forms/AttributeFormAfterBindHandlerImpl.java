package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.TARGET;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerAfterBindHandlerImpl;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

/**
 * Общие действия после окончания биндинга всех свойств - посылаем события свойств ATTR_TYPE и TARGET, 
 * чтобы отработали ValueChangeHandler свойств, которые зависят от них.
 * <AUTHOR>
 * @since 16.05.2012
 */
public class AttributeFormAfterBindHandlerImpl<F extends ObjectForm> extends PropertyContainerAfterBindHandlerImpl
        implements AttributeFormAfterBindHandler<F>
{
    @Inject
    AttributeFormContextPropertiesSetter<F> contextPropsSetter;

    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        super.onAfterContainerBind(context);
        Object defaultValue = context.getPropertyValues().getProperty(DEFAULT_VALUE);
        context.getPropertyControllers().get(ATTR_TYPE).setValue();
        context.getPropertyControllers().get(TARGET).setValue();
        context.getPropertyValues().setProperty(DEFAULT_VALUE, defaultValue);
        contextPropsSetter.setContextProperties(context);
        context.getDisplay().setFixed(false);
        context.getDisplay().display();
    }
}
