package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 *
 */
public class InputmaskRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, String, FootedTextBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, FootedTextBoxProperty property,
            AsyncCallback<Boole<PERSON>> callback)
    {
        boolean hasInputMask = Presentations.STRING_EDIT_WITH_MASK.equals(context.getPropertyValues().getProperty(
                AttributeFormPropertyCode.EDIT_PRS));
        if (!hasInputMask)
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK, "");
        }
        else
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.INPUTMASK, property.getValue());
        }
        callback.onSuccess(hasInputMask);
    }
}