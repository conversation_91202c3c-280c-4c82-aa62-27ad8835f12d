package ru.naumen.metainfoadmin.client.scheduler.forms;

import java.util.Map.Entry;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreatorFactory;

/**
 * <AUTHOR>
 * @since 02.06.2011
 *
 */
public class AddTriggerFormPresenter extends TriggerFormPresenter
{
    @Inject
    public AddTriggerFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.addingTrigger());
        initTypes();
        getDisplay().setFixed(false);
        getDisplay().display();
    }

    private void initTypes()
    {
        SingleSelectCellList<?> selList = type.getValueWidget();
        for (Entry<String, TriggerCreatorFactory.Factory> triggerType : factory.getCreators().entrySet())
        {
            selList.addItem(triggerType.getValue().getTitle(), triggerType.getKey());
        }
    }
}
