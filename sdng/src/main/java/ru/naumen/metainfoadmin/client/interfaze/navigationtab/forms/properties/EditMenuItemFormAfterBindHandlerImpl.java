package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Абстрактный хэндлер после биндинга формы редактирования элемента меню
 * <AUTHOR>
 * @since 16 окт. 2013 г.
 */
public abstract class EditMenuItemFormAfterBindHandlerImpl<M extends IMenuItem> extends ObjectFormAfterBindHandlerImpl<ObjectFormEdit, M>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        context.setDisabled(MenuItemPropertyCode.TYPE);
        super.onAfterContainerBind(context);
    }
}