package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.ABBREVIATION;

import com.google.inject.Singleton;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;

/**
 * Делегат обновления значения атрибута "Сокращения" элемента левого меню
 * <AUTHOR>
 * @since 24.07.2020
 */
@Singleton
public class MenuSettingsAbbreviationVCHDelegateImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String abbrev = context.getPropertyValues().<String> getProperty(ABBREVIATION);
        if (StringUtilities.isNotEmpty(abbrev))
        {
            context.getPropertyValues().setProperty(ABBREVIATION, abbrev.toUpperCase());
            context.getPropertyControllers().get(ABBREVIATION).refresh();
        }
    }

}
