package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.GenerationRuleDelegateHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 10 дек. 2013 г.
 */
public class UseGenerationRuleRefreshDelegateEditImpl extends UseGenerationRuleRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Attribute attr = (Attribute)context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);

        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);

        if (Employee.FQN.isSameClass(metaClass.getFqn()) && AbstractBO.TITLE.equals(attrCode))
        {
            callback.onSuccess(false);
            return;
        }

        if (GenerationRuleDelegateHelper.shouldBeDisabled(attr))
        {
            property.setDisable();
        }

        super.refreshProperty(context, property, callback);
    }
}
