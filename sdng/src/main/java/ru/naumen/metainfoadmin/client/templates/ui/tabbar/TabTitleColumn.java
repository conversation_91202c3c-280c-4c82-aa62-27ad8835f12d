package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import jakarta.inject.Inject;

import ru.naumen.core.client.listeditor.ListEditorResources;
import ru.naumen.core.client.widgets.columns.TitleColumn;

/**
 * Столбец для отображения названия шаблона вкладки.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class TabTitleColumn extends TitleColumn<TabModel>
{
    @Inject
    public void init(ListEditorResources resources)
    {
        resources.cellTableStyle().ensureInjected();
        setCellStyleNames(resources.cellTableStyle().titleColumn());
    }
}
