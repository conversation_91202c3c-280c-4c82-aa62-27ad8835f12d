package ru.naumen.metainfoadmin.client.templates.content.command;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.content.form.EditContentTemplateFormPresenter;

/**
 * Команда редактирования шаблона контента.
 * <AUTHOR>
 * @since Mar 25, 2021
 */
public class EditContentTemplateCommand extends PresenterCommandImpl<DtObject, DtObject, DtObject>
{
    private final Provider<EditContentTemplateFormPresenter> editFormPresenterProvider;

    @Inject
    public EditContentTemplateCommand(@Assisted CommandParam<DtObject, DtObject> param,
            Provider<EditContentTemplateFormPresenter> editFormPresenterProvider)
    {
        super(param);
        this.editFormPresenterProvider = editFormPresenterProvider;
    }

    @Override
    public void onExecute(DtObject result, CallbackDecorator<DtObject, DtObject> callback)
    {
        callback.onSuccess(result);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<DtObject, DtObject> getPresenter(DtObject value)
    {
        return editFormPresenterProvider.get();
    }
}
