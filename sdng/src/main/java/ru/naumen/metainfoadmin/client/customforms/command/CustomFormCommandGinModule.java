package ru.naumen.metainfoadmin.client.customforms.command;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 21.04.2016
 *
 */
public class CustomFormCommandGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        bind(CustomFormCommandFactoryInitializer.class).asEagerSingleton();
        
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, new TypeLiteral<AddCustomFormCommand>(){})
            .build(new TypeLiteral<CommandProvider<AddCustomFormCommand, ContentCommandParam<CustomForm, CustomForm>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditCustomFormCommand.class)
            .build(new TypeLiteral<CommandProvider<EditCustomFormCommand, ContentCommandParam<CustomForm, CustomForm>>>() {}));        
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DeleteCustomFormCommand.class)
            .build(new TypeLiteral<CommandProvider<DeleteCustomFormCommand, ContentCommandParam<CustomForm, Void>>>() {}));        
      //@formatter:on
    }
}
