package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandlerImpl;

/**
 * Хэндлер после биндинга формы добавления элемента меню
 *
 * <AUTHOR>
 * @since 18 окт. 2013 г.
 */
public class AddMenuItemFormAfterBindHandlerImpl<M extends IMenuItem> extends ObjectFormAfterBindHandlerImpl<ObjectFormAdd, M>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(ReferenceCode.REFERENCE_VALUE).getValue();
        super.onAfterContainerBind(context);
    }
}
