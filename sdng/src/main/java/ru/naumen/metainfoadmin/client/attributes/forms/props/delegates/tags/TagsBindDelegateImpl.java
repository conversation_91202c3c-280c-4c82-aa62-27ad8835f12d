package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.tags;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Делегат привязки свойства "Метки".
 * <AUTHOR>
 * @since Dec 20, 2018
 */
public class TagsBindDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateBind<F, Collection<SelectItem>, TagsProperty>
{
    @Override
    public void bindProperty(PropertyContainerContext context, TagsProperty property, AsyncCallback<Void> callback)
    {
        context.getContextValues().setProperty(AttributeFormContextValues.PENDING_TAGS, property.getPendingTags());
        property.reload(callback);
    }
}
