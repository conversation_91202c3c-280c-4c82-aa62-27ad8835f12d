package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuItemTypeOfCard;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;

/**
 * Делегат отрисовки свойства "Ссылка на карточку" для виджета {@link HomePage#REFERENCE_CARD_TYPE}
 *
 * <AUTHOR>
 * @since 17.01.2023
 */
public class HomePageTypeOfCardBindDelegate extends PropertyDelegateBindImpl<SelectItem, ListBoxProperty>
{
    private final Map<MenuItemTypeOfCard, String> referenceCardTypes;

    @Inject
    public HomePageTypeOfCardBindDelegate(
            @Named(NavigationTabSettingsGinModule.REFERENCE_CARD_TYPE_TITLES)
            Map<MenuItemTypeOfCard, String> referenceCardTypes)
    {
        this.referenceCardTypes = referenceCardTypes;
    }

    @Override
    public void bindProperty(final PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Void> callback)
    {
        final SingleSelectCellList<?> selectList = property.getValueWidget();
        for (MenuItemTypeOfCard type : MenuItemTypeOfCard.values())
        {
            selectList.addItem(referenceCardTypes.get(type), type.name());
        }
        String currentObject = context.getPropertyValues().getProperty(HomePage.REFERENCE_CARD_TYPE, null);
        DtObject value = StringUtilities.isEmpty(currentObject) ?
                selectList.getItem(0) :
                selectList.getItem(currentObject);
        property.trySetObjValue(value.getUUID());
        context.setProperty(HomePage.REFERENCE_CARD_TYPE, value.getUUID());
        callback.onSuccess(null);
    }
}
