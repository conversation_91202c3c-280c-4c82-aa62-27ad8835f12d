package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.script.js.CustomJSElement.TargetPlace;
import ru.naumen.metainfoadmin.client.script.js.CustomJSElementsMessages;

/**
 * Фабрика виджетов для атрибута "Место применения" файла кастомизации.
 * <AUTHOR>
 * @since Dec 10, 2017 
 */
public class AttributeCustomJSTargetWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;
    @Inject
    private CustomJSElementsMessages messages;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();
        widget.addItem(new SimpleDtObject(TargetPlace.global.name(), messages.targetGlobal()));
        widget.addItem(new SimpleDtObject(TargetPlace.adminInterface.name(), messages.targetAdmin()));
        widget.addItem(new SimpleDtObject(TargetPlace.operatorInterface.name(), messages.targetOperator()));
        return widget;
    }

    @Override
    public void create(PresentationContext context, AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        callback.onSuccess(create(context));
    }
}
