package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Фабрика контроллера свойства {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#ATTRIBUTE_CHAIN}
 *
 * <AUTHOR>
 * @since 28.01.2023
 */
public interface HomePageAttrChainControllerFactory extends PropertyControllerSyncFactoryInj<RelationsAttrTreeObject,
        PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>>
{
}
