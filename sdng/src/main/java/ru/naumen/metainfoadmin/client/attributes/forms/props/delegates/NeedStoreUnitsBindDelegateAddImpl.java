package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;

/**
 * Делегат, осуществляющий биндинг свойства "Запоминать выбранные единицы измерения" 
 * <AUTHOR>
 * @since 07.04.2017
 */
public class NeedStoreUnitsBindDelegateAddImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<Boolean, BooleanCheckBoxInfoProperty>
        implements AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxInfoProperty>
{
    @Inject
    private AttributesMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, BooleanCheckBoxInfoProperty property,
            AsyncCallback<Void> callback)
    {
        property.setInfo(messages.needStoreUnitsInfo());
        super.bindProperty(context, property, callback);
    }
}
