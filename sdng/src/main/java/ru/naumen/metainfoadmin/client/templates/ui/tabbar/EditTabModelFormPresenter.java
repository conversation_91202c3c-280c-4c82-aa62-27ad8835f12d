package ru.naumen.metainfoadmin.client.templates.ui.tabbar;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateMessages;

/**
 * Представление формы изменения шаблона вкладки.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class EditTabModelFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
{
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> originalTitle;

    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private Processor validation;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private UITemplateMessages templateMessages;

    private TabModel model;
    private AsyncCallback<TabModel> callback;

    @Inject
    public EditTabModelFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(TabModel model, AsyncCallback<TabModel> callback)
    {
        this.model = model;
        this.callback = callback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        TabModel result = new TabModel();
        result.setTitle(title.getValue());
        result.setCode(model.getCode());
        result.setOriginalTitle(model.getOriginalTitle());
        result.setEnabled(model.isEnabled());
        callback.onSuccess(result);
        unbind();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(commonMessages.tabEditing());
        bindProperties();
        ensureDebugIds();
        setValues();
        getDisplay().display();
    }

    private void bindProperties()
    {
        title.setCaption(templateMessages.tabTitle());
        title.setValidationMarker(true);
        title.setMaxLength(ContentUtils.MAX_TITLE_LENGTH);
        getDisplay().add(title);
        validation.validate(title, notEmptyValidator);

        code.setCaption(commonMessages.code());
        code.setDisable();
        getDisplay().add(code);

        originalTitle.setCaption(templateMessages.originalTabTitle());
        originalTitle.setDisable();
        getDisplay().add(originalTitle);
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(originalTitle, "originalTitle");
    }

    private void setValues()
    {
        title.setValue(model.getTitle());
        code.setValue(model.getCode());
        originalTitle.setValue(model.getOriginalTitle());
    }
}
