package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import static ru.naumen.metainfo.shared.Constants.HomePage.REFERENCE_CARD_TYPE;
import static ru.naumen.metainfo.shared.Constants.HomePage.REFERENCE_TAB_VALUE;
import static ru.naumen.metainfo.shared.Constants.HomePage.TYPE;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.ATTRIBUTE_CHAIN;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CASES;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CLASS;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.REFERENCE_VALUE;

import java.util.HashSet;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.homepage.HomePageType;

/**
 * Делегат изменения значения свойства "Вид элемента" {@link HomePage#TYPE}
 *
 * <AUTHOR>
 * @since 17.01.2023
 */
public class HomePageTypeVCHDelegate implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String type = context.getPropertyValues().getProperty(TYPE);
        if (!HomePageType.REFERENCE.name().equals(type))
        {
            context.setProperty(REFERENCE_TAB_VALUE, null);
            context.setProperty(ATTRIBUTE_CHAIN, null);
            context.setProperty(REFERENCE_CARD_TYPE, null);
            context.setProperty(REFERENCE_VALUE, null);
            context.setProperty(OBJECT_CLASS, null);
            context.setProperty(OBJECT_CASES, new HashSet<>());
        }
        context.getRefreshProcess().startForContainer();
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}