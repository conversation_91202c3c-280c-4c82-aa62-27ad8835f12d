package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.Constants.LeftMenuRootValue;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;

/**
 * Делегат обновления свойства "Метки" для главной родительской плитки
 * <AUTHOR>
 * @since 19.04.2021
 */
public class MenuSettingsTagsRefreshDelegateImpl implements PropertyDelegateRefresh<Collection<SelectItem>,
        TagsProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, TagsProperty property,
            AsyncCallback<Boolean> callback)
    {
        LeftMenuItemSettingsDTO menuItem = context.getContextValues().getProperty(MenuItemPropertyCode.TYPE);
        callback.onSuccess(menuItem != null && menuItem.getCode().equals(LeftMenuRootValue.CODE));
    }
}