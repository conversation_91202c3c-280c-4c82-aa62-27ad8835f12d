package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule;

/**
 * Делегат обновления свойства "Форматирование" элемента левого меню
 * <AUTHOR>
 * @since 27.06.2020
 */
public class LeftMenuFormattingRefreshDelegateImpl
        implements PropertyDelegateRefresh<SelectItem, ListBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(
                EditNavigationSettingsFormGinModule.MenuItemPropertyCode.TYPE);
        IMenuItem.MenuItemType type = IMenuItem.MenuItemType.valueOf(typeStr);
        callback.onSuccess(IMenuItem.MenuItemType.chapter.equals(type));
    }
}