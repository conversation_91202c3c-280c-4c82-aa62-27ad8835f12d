package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;
import com.google.inject.Provider;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dispatch.GetSystemDtObjectListAction;
import ru.naumen.core.shared.dispatch.GetSystemDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.SystemObjectAttributeType;

/**
 * Фабрика виджетов для редактирования значения атрибута типа "Ссылка на системный объект"
 *
 * <AUTHOR>
 *
 */
public class AttributeSystemObjectSelectWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;
    @Inject
    DispatchAsync service;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        String code = context.getAttributeType().<SystemObjectAttributeType> cast().getRelatedObject();
        service.execute(new GetSystemDtObjectListAction(code), new BasicCallback<GetSystemDtObjectListResponse>()
        {
            @Override
            public void handleSuccess(GetSystemDtObjectListResponse response)
            {
                SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

                Collection<DtObject> dtObjects = response.getObjects();

                for (DtObject dto : dtObjects)
                {
                    widget.addItem(dto);
                }

                callback.onSuccess(widget);
            }
        });

    }

}
