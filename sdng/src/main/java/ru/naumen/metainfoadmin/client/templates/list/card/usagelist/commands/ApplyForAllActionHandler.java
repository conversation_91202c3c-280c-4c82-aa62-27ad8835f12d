package ru.naumen.metainfoadmin.client.templates.list.card.usagelist.commands;

import java.util.Objects;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateUsagePointsAction;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateUsagePointsResponse;
import ru.naumen.metainfoadmin.client.dynadmin.UIContextDecorator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.AdvlistUIContext;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandler;

/**
 * Обработчик действия применения изменений шаблона списка ко всем местам использования
 * <AUTHOR>
 * @since 28.08.2018
 */
public class ApplyForAllActionHandler extends AdminActionHandler<ApplyForAllAction>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Dialogs dialogs;
    @Inject
    private ListTemplatesMessages listTemplatesMessages;

    @Inject
    public ApplyForAllActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<ApplyForAllAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @Override
    public void doExecute()
    {
        DtObject template = getContext().getObject();
        if ((boolean)Objects.requireNonNull(template).get(ListTemplate.IS_USED))
        {
            String templateCode = template.getUUID();
            dispatch.execute(new ApplyListTemplateUsagePointsAction(templateCode, null),
                    new BasicCallback<ApplyListTemplateUsagePointsResponse>()
                    {
                        @Override
                        protected void handleSuccess(ApplyListTemplateUsagePointsResponse result)
                        {
                            if (!result.getMessage().isEmpty())
                            {
                                dialogs.info(result.getMessage());
                                context.getParentContext().getEventBus().fireEvent(new RefreshContentEvent(context
                                        .getParentContent()));
                            }
                            else
                            {
                                dialogs.info(listTemplatesMessages.applySuccessfully());
                            }
                        }
                    });
        }
    }

    protected AdvlistUIContext getContext()
    {
        return ((AdvlistUIContext)((UIContextDecorator)Objects.requireNonNull(
                ((AdvlistUIContext)context.getParentContext()).getParentContext())).getAdaptee());
    }
}
