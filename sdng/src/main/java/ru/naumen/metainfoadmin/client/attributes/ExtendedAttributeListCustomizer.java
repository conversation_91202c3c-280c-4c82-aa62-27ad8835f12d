package ru.naumen.metainfoadmin.client.attributes;

import static ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.COL_SORTER_SYSTEM_FIRST;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.Multimap;
import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.IFrameElement;
import com.google.gwt.dom.client.Style.Cursor;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SingleSelectionModel;
import com.google.inject.name.Named;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.DocumentUtils;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.RichTextView;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.core.client.widgets.grouplist.GroupList.GroupInfo;
import ru.naumen.core.client.widgets.grouplist.TableRowComparator;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectCell;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectDisplayImpl;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule;
import ru.naumen.metainfoadmin.client.attributes.columns.ColumnSorter;
import ru.naumen.metainfoadmin.client.attributes.columns.ColumnSorterCellModel;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.client.wf.statesetting.columns.PopupStateSettingButtonColumnBase.Resources;

/**
 * Реализация логики отрисовки списка атрибутов на карточке класса/типа.
 * См.: https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63374877 
 *
 * <AUTHOR>
 * @since 23 авг. 2018 г.
 *
 */
public class ExtendedAttributeListCustomizer implements AttributeListCustomizer
{
    /**
     * Сначала системные атрибуты, потом пользовательские.
     * Внутри группы сортировка по названию.
     *
     * <AUTHOR>
     * @since 24 авг. 2018 г.
     *
     */
    private static class SystemAttrsFirstByTitleComparator implements Comparator<Attribute>
    {
        @Override
        public int compare(Attribute a1, Attribute a2)
        {
            if (a1.isHardcoded() == a2.isHardcoded())
            {
                return a1.getTitle().compareTo(a2.getTitle());
            }
            return a1.isHardcoded() ? -1 : 1;
        }
    }

    private class AttrListSorterSelectItem
    {
        private AttributeListImpl attrList;
        private ColumnSorter<?> sorter;

        public AttrListSorterSelectItem(AttributeListImpl attrList, ColumnSorter<?> sorter)
        {
            this.attrList = attrList;
            this.sorter = sorter;
        }

        public ColumnSorter<?> getSorter()
        {
            return sorter;
        }

        public AttributeListImpl getAttrList()
        {
            return attrList;
        }
    }

    private class AttrListSorterSelectListCell extends AbstractCell<AttrListSorterSelectItem> implements
            PopupListSelectCell<AttrListSorterSelectItem>
    {

        @Override
        public void render(com.google.gwt.cell.client.Cell.Context context, AttrListSorterSelectItem value,
                SafeHtmlBuilder sb)
        {
            sb.appendHtmlConstant(value.getSorter().getSortingControlText());
        }
    }

    protected static Logger LOG = Logger.getLogger(ExtendedAttributeListCustomizer.class.getName());

    private static final String SORT_POPUP_DEBUG_ID = "sortPopup";

    private static final String COLSPAN_PROPERTY = "colspan";
    private static final String ROWSPAN_PROPERTY = "rowspan";

    @Inject
    @Named(AttributeListColumnGinModule.ATTR_LIST_READY_STATE)
    private ReadyState rs;

    @Inject
    private AttributesMessages messages;

    private static Resources RESOURCES;

    //связывает код агрегирующего атрибута с агрегируемым атрибутом
    private Map<String, List<Attribute>> aggregating = new HashMap<>();

    //Набор кодов агрегируемых атрибутов
    private Set<String> aggregatesCodes = new HashSet<>();

    //Все сортировщики, доступные в таблице атрибутов
    private Set<ColumnSorter<?>> sorters = new HashSet<>();

    @Nullable
    private ColumnSorter<?> currentActiveColumnSorter = null;

    @Inject
    private RichTextView rtv;
    @Inject
    private AdminCachedMetainfoService metainfoService;

    /**
     * Хранилище созданных всплывающих меню для возможности управления их свойствами
     */
    private Set<PopupListSelectDisplayImpl<?, ?>> popups = new HashSet<>();

    @Override
    public Element createAndFillHeader(List<ColumnInfo<Attribute>> columns, AttributeListImpl attributeList,
            RegistrationContainer registrationContainer)
    {
        sorters.clear();
        popups.clear();
        Element header, tHead;
        if (useStickyHeader())
        {
            header = DOM.createTable();
            header.addClassName(AdminWidgetResources.INSTANCE.tables().tableElems());
            header.addClassName(AdminWidgetResources.INSTANCE.additional().attrsHeader());
            tHead = DOM.createTHead();
            DOM.appendChild(header, tHead);
        }
        else
        {
            header = DOM.createTHead();
            tHead = header;
        }

        //Заголовок двухуровневый
        Element firstRow = DOM.createTR();
        Element secondRow = DOM.createTR();
        List<Element> rows = Arrays.asList(firstRow, secondRow);
        DOM.appendChild(tHead, firstRow);
        DOM.appendChild(tHead, secondRow);
        for (Element row : rows)
        {
            int consColumnCurrentColSpan = 0;
            for (ColumnInfo<Attribute> column : columns)
            {
                boolean sortingPossible = false;
                if (column != null)
                {
                    Element cell = null;
                    String innerText = column.getTitle();
                    String style = column.getStyle();
                    String width = column.getWidth();
                    String hintTitle = column.getHintTitle();
                    if (row.equals(firstRow))
                    {
                        if (column.getConsolidatingColumn() == null)
                        {
                            cell = DOM.createTD();
                            cell.setAttribute(ROWSPAN_PROPERTY, String.valueOf(rows.size()));
                            attributeList.setLowerLevelTdElements(column, cell);
                            sortingPossible = true;
                        }
                        else
                        {
                            if (consColumnCurrentColSpan == 0)
                            {
                                cell = DOM.createTD();
                                cell.setAttribute(COLSPAN_PROPERTY,
                                        String.valueOf(column.getConsolidatingColumnColspan()));
                                innerText = column.getConsolidatingColumn().getTitle();
                                style = column.getConsolidatingColumn().getStyle();
                                width = column.getConsolidatingColumn().getWidth();
                                hintTitle = column.getConsolidatingColumn().getHintTitle();
                            }
                            consColumnCurrentColSpan++;
                            if (consColumnCurrentColSpan == column.getConsolidatingColumnColspan())
                            {
                                consColumnCurrentColSpan = 0;
                            }
                        }
                    }
                    else
                    {
                        if (column.getConsolidatingColumn() != null)
                        {
                            cell = DOM.createTD();
                            attributeList.setLowerLevelTdElements(column, cell);
                            sortingPossible = true;
                        }
                    }
                    if (cell != null)
                    {
                        DOM.appendChild(row, cell);
                        Label headerTitle = new Label(innerText);
                        headerTitle.addStyleName(AdminWidgetResources.INSTANCE.attributeList().headerTitle());
                        headerTitle.setWidth("auto");
                        cell.appendChild(headerTitle.getElement());

                        if (!StringUtilities.isEmpty(style))
                        {
                            cell.addClassName(style);
                        }
                        if (!StringUtilities.isEmpty(width))
                        {
                            column.setWidth(width);
                        }
                        if (!StringUtilities.isEmpty(hintTitle))
                        {
                            cell.setTitle(hintTitle);
                        }
                        if (sortingPossible)
                        {
                            createSorting(cell, headerTitle.getElement(), column, attributeList, registrationContainer);
                        }
                    }
                }
            }
        }
        return header;
    }

    /**
     * Добавляет к колонке функциональность сортировки, если она определена
     */
    private void createSorting(Element headerCell, Element controlElement, ColumnInfo<Attribute> columnInfo,
            AttributeListImpl attributeList, RegistrationContainer registrationContainer)
    {
        if (columnInfo.getColumnSorters() != null && !columnInfo.getColumnSorters().isEmpty())
        {
            columnInfo.getColumnSorters().forEach(s -> s.setColumnSorterCellModel(
                    new ColumnSorterCellModel(headerCell, controlElement, messages)));
            if (columnInfo.getColumnSorters().size() > 1)
            {
                columnInfo.getColumnSorters().forEach(sorter -> sorter.setComplex(true));
            }
            sorters.addAll(columnInfo.getColumnSorters());
            controlElement.getStyle().setCursor(Cursor.POINTER);
            Event.sinkEvents(controlElement, Event.ONCLICK);
            Event.setEventListener(controlElement, event ->
            {
                if (Event.ONCLICK == event.getTypeInt())
                {
                    if (columnInfo.getColumnSorters().size() == 1)
                    {
                        applySorting(attributeList, columnInfo.getColumnSorters().get(0), true);
                    }
                    else
                    {
                        // @formatter:off
                        PopupListSelectDisplayImpl<AttrListSorterSelectItem, SingleSelectionModel<AttrListSorterSelectItem>> popup = 
                                new PopupListSelectDisplayImpl<AttrListSorterSelectItem, SingleSelectionModel<AttrListSorterSelectItem>>(
                                    new AttrListSorterSelectListCell(), getResources(),
                                    new ListDataProvider<AttrListSorterSelectItem>(),
                                    new SingleSelectionModel<AttrListSorterSelectItem>(),
                                    DefaultSelectionEventManager.<AttrListSorterSelectItem> createDefaultManager());
                        // @formatter:on
                        popup.ensureDebugId(SORT_POPUP_DEBUG_ID);
                        popups.add(popup);
                        popup.getList().setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
                        addCellPreviewHandler(popup, registrationContainer);

                        ArrayList<AttrListSorterSelectItem> list = new ArrayList<>();
                        for (ColumnSorter<?> sorter : columnInfo.getColumnSorters())
                        {
                            list.add(new AttrListSorterSelectItem(attributeList, sorter));
                        }
                        popup.getListProvider().setList(list);

                        int x = event.getClientX();
                        int y = event.getClientY();
                        Element scrollableElement = RootPanel.getScrollableRootPanelElement();

                        if (scrollableElement != null)
                        {
                            x -= scrollableElement.getAbsoluteLeft() - scrollableElement.getScrollLeft();
                            y -= scrollableElement.getAbsoluteTop() - scrollableElement.getScrollTop();
                        }
                        popup.setPopupPosition(x, y);
                        popup.show();
                    }
                }
            });
        }
    }

    private void applySorting(AttributeListImpl attributeList, ColumnSorter<?> currentSorter,
            boolean needSwitchDirection)
    {
        ColumnSorter<?> actualSorter = ensureColumnSorterActual(currentSorter);
        if (actualSorter != null)
        {
            if (needSwitchDirection)
            {
                actualSorter.getRowComparator().switchDirection();
            }
            attributeList.sortTable(actualSorter.getRowComparator());
            setCurrentColumnSorter(actualSorter);
        }
    }

    /**
     * Сортировщик может быть пересоздан, но id у него остается неизменным.
     * Ищем сортировщик с данным id среди "живых" и возвращаем его.
     * @param columnSorter
     * @return актуальный сортировщик с таким же id, как у сортировщика переданного в параметре либо null, если не
     * найден.
     */
    private ColumnSorter<?> ensureColumnSorterActual(ColumnSorter<?> columnSorter)
    {
        ColumnSorter<?> result = columnSorter != null ? sorters.stream()
                .filter(columnSorter::equals).findFirst().orElse(null) : null;
        if (result != null)
        {
            result.getRowComparator().setDirect(columnSorter.getRowComparator().isDirect());
        }
        return result;
    }

    /**
     * Устанавливает активный сортировщик колонок (в каждый момент времени он может быть только один)
     * @param currentSorter
     */
    private void setCurrentColumnSorter(ColumnSorter<?> currentSorter)
    {
        for (ColumnSorter<?> sorter : sorters)
        {
            sorter.setActive(sorter.equals(currentSorter));
            sorter.updateSortingElement();
        }
        //Так как несколько сортировщиков могут иметь один и тот же элемент сортировки,
        //его нужно дополнительно обновить для текущего активного сортировщика 
        currentSorter.updateSortingElement();
        currentActiveColumnSorter = currentSorter;
    }

    private static Resources getResources()
    {
        if (RESOURCES == null)
        {
            RESOURCES = GWT.create(Resources.class);
        }
        return RESOURCES;
    }

    public void addCellPreviewHandler(final PopupListSelectDisplayImpl<AttrListSorterSelectItem,
            SingleSelectionModel<AttrListSorterSelectItem>> popup, RegistrationContainer registrationContainer)
    {
        registrationContainer.registerHandler(popup.getList()
                .addCellPreviewHandler(event ->
                {
                    if (!BrowserEvents.CLICK.equals(event.getNativeEvent().getType()))
                    {
                        return;
                    }
                    popup.destroy();
                    applySorting(event.getValue().getAttrList(), event.getValue().getSorter(), true);
                }));
    }

    @Override
    public void fillGroupRow(GroupInfo<String> group, int row, Multimap<String, ButtonToolDisplay> buttons,
            FlexTable grid, AttributeListImpl attributeList)
    {
        //Группировка атрибутов пока не предполагается        
    }

    @Override
    public boolean fillGroupContent(Collection<Attribute> elementsWithoutAggregates, FlexTable grid,
            AttributeListImpl attributeList)
    {
        for (Attribute element : elementsWithoutAggregates)
        {
            attributeList.fillRow(attributeList.insertRow(), element);
            if (aggregating.containsKey(element.getCode()))
            {
                for (Attribute attr : aggregating.get(element.getCode()))
                {
                    int beforeRow = attributeList.insertRow();
                    attributeList.fillRow(beforeRow, attr);
                    grid.getCellFormatter()
                            .addStyleName(beforeRow, 0, AdminWidgetResources.INSTANCE.attributeList().aggregateAttr());
                }
            }
        }
        return true;
    }

    private void fillContent(Comparator<Attribute> sorting, Map<Attribute, Integer> elements,
            AttributeListImpl attributeList)
    {
        //Агрегируемые атрибуты должны отображаться всегда под агрегирующими, 
        //независимо от правил сортировки и их названий, поэтому исключаем 
        //агрегируемые атрибуты из исходной коллекции 
        //(они будут добавлены при вставке агрегирующего) 
        aggregating.clear();
        List<Attribute> withoutAggregates = new ArrayList<>(elements.keySet());

        for (Attribute attr : elements.keySet())
        {
            String aggregator = attr.getType().getProperty(Constants.ObjectAttributeType.AGGREGATOR);
            if (aggregator != null && !attr.getType().isAttributeOfRelatedObject())
            {
                List<Attribute> aggregates = aggregating.get(aggregator);
                if (aggregates == null)
                {
                    aggregates = new ArrayList<>();
                }
                aggregates.add(attr);
                aggregating.put(aggregator, aggregates);
                withoutAggregates.remove(attr);
            }
        }
        updateAggregateCodes();
        //Группировка атрибутов пока не предполагается, поэтому работаем с единым списком
        withoutAggregates.sort(sorting);
        attributeList.fillGroupContent(withoutAggregates);
    }

    /**
     * Формирует набор кодов агрегируемых атрибутов
     */
    private void updateAggregateCodes()
    {
        aggregatesCodes.clear();
        aggregating.values().forEach(attrs ->
        {
            aggregatesCodes.addAll(attrs.stream().map(Attribute::getCode).collect(Collectors.toList()));
        });
    }

    @Override
    public boolean fillContent(Map<Attribute, Integer> elements, AttributeListImpl attributeList)
    {
        fillContent(new SystemAttrsFirstByTitleComparator(), elements, attributeList);
        // После отрисовки таблицы с атрибутами нужно инвалидировать кэш,
        // т.к. далее, возможно, кто-нить будет изменять метаинфу
        metainfoService.invalidateCache();
        return true;
    }

    @Override
    public void refresh(MetaClass metainfo, AttributeListImpl attributeList)
    {
        attributeList.clear();
        attributeList.addElements(metainfo.getAttributes());
        attributeList.refresh();
        attributeList.syncWidth();
        rs.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                //Если сортировщик не определен - пробуем найти сортировщик по умолчанию
                if (currentActiveColumnSorter == null)
                {
                    currentActiveColumnSorter = getDefaultColumnSorter();
                }
                if (currentActiveColumnSorter != null)
                {
                    currentActiveColumnSorter.setActive(true);
                    applySorting(attributeList, currentActiveColumnSorter, false);
                }
                if (attributeList.getAddedAttributeCode() != null)
                {
                    attributeList.scrollToAttributeAndHighlightIt(attributeList.getAddedAttributeCode());
                    attributeList.removeAddAttributeActionFromContext();
                }
                attributeList.handleContainerScrollEvent();
                Scheduler.get().scheduleDeferred(attributeList::syncWidth);
            }
        });
    }

    private ColumnSorter<?> getDefaultColumnSorter()
    {
        //Ищем сортировщик по типу атрибута в имеющихся (сначала системные, потом пользовательские)
        return sorters.stream().filter(sorter -> COL_SORTER_SYSTEM_FIRST.equals(sorter.getId())).findFirst().orElse(
                null);
    }

    @Override
    public boolean useStickyHeader()
    {
        return true;
    }

    @Override
    public void sortTable(TableRowComparator<?> rowComparator, AttributeListImpl attributeList)
    {
        int startIndex = useStickyHeader() ? 0 : attributeList.getHeaderRowsCount();
        List<Element> rows = new ArrayList<Element>();
        Map<String, Element> aggregatesRows = new HashMap<>();
        for (int rowIndex = startIndex; rowIndex < attributeList.getRowCount(); rowIndex++)
        {
            Element row = attributeList.getRow(rowIndex);
            String code = AttrWidgetsHelper.getAttributeCode(row);
            //Сразу же отделяем агрегируемые атрибуты
            if (aggregatesCodes.contains(code))
            {
                aggregatesRows.put(code, row);
            }
            else
            {
                rows.add(row);
            }
        }
        rows.sort(rowComparator);
        Element tbody = attributeList.getTBodyElement();
        int aggregatesCounter = 0;
        for (int i = 0; i < rows.size(); i++)
        {
            Element newChild = rowComparator.isDirect() ? rows.get(i) : rows.get(rows.size() - 1 - i);
            handleRtfTextDefaultValue(newChild);
            tbody.insertBefore(newChild, attributeList.getRow(startIndex + i + aggregatesCounter));
            //Если текущий атрибут - агрегирующий, то добавляем агрегируемые после него
            String attrCode = AttrWidgetsHelper.getAttributeCode(newChild);
            if (attrCode != null && aggregating.keySet().contains(attrCode))
            {
                for (Attribute attr : aggregating.get(attrCode))
                {
                    tbody.insertBefore(aggregatesRows.get(attr.getCode()), attributeList.getRow(startIndex + i
                                                                                                + ++aggregatesCounter));
                }
            }
        }
    }

    /**
     * Текст в формате RTF отображается в iframe, который инициализируется при добавлении в дерево DOM.
     * При этом содержимое iframe затерается, поэтому установить его синхронно не получается.
     * Логика по установке содержимого реализована в {@link RichTextView#onLoadRtf(JavaScriptObject)},
     * нам нужно лишь подготовить ее.
     * @param row строка, которую нужно проверить на предмет содержания текста RTF в значении по умолчанию  
     */
    private void handleRtfTextDefaultValue(Element row)
    {
        Element el = getDefaultValueColumn(row);
        if (el != null)
        {
            while (el.getFirstChildElement() != null)
            {
                el = el.getFirstChildElement();
            }
            if (IFrameElement.TAG.equalsIgnoreCase(el.getTagName()))
            {
                IFrameElement iframe = IFrameElement.as(el);
                Document contentDocument = DocumentUtils.getContentDocument(iframe);
                if (!StringUtilities.isEmpty(iframe.getId()) && null != contentDocument
                    && contentDocument.getBody().hasChildNodes())
                {
                    String value = "";
                    for (int i = 0; i < contentDocument.getBody().getChildCount(); i++)
                    {
                        if (Element.is(contentDocument.getBody().getChild(i)))
                        {
                            value += Element.as(contentDocument.getBody().getChild(i)).getString();
                        }
                    }
                    rtv.setContent(iframe.getId(), value);
                }
            }
        }
    }

    /**
     * Возвращает элемент, соответствующий ячейке колонки "По умолчанию"
     * @param row строка, в которой нужно найти ячейку "По умолчанию"
     * @return элемент ячейки "По умолчанию" либо null, если не найдена
     */
    private Element getDefaultValueColumn(Element row)
    {
        if (row != null)
        {
            for (int i = 0; i < row.getChildCount(); i++)
            {
                if (row.getChild(i) instanceof Element && ((Element)row.getChild(i)).hasClassName(
                        AdminWidgetResources.INSTANCE.attributeList().byDefault()))
                {
                    return (Element)row.getChild(i);
                }
            }
        }
        return null;
    }

    public Set<PopupListSelectDisplayImpl<?, ?>> getPopups()
    {
        return popups;
    }
}
