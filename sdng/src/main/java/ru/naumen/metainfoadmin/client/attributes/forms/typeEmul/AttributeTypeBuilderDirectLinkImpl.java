package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
@Singleton
public class AttributeTypeBuilderDirectLinkImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(ObjectAttributeType.METACLASS_FQN,
                propertyValues.getProperty(AttributeFormPropertyCode.TARGET_CLASS));
        AttrWidgetsHelper.setPermittedTypes(type, propertyValues);
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        propertyValues.setProperty(AttributeFormPropertyCode.TARGET_CLASS,
                type.getProperty(ObjectAttributeType.METACLASS_FQN));
    }
}
