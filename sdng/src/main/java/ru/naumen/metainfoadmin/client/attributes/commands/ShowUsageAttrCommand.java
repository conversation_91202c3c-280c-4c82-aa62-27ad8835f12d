package ru.naumen.metainfoadmin.client.attributes.commands;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.usage.ShowUsageGinModule.ShowUsageAttrPresenterFactory;

/**
 * Команда отображения формы "Используется в настройках"
 * <AUTHOR>
 * @since 14 Jun 18
 */
public class ShowUsageAttrCommand extends PresenterCommandImpl<Attribute, Void, MetaClass>
{
    private ShowUsageAttrPresenterFactory presenterFactory;
    private final Context context;

    @Inject
    public ShowUsageAttrCommand(@Assisted AttributeCommandParam param, ShowUsageAttrPresenterFactory presenterFactory)
    {
        super(param);
        context = param.getContext();
        this.presenterFactory = presenterFactory;
    }

    @Override
    public void onExecute(MetaClass result, CallbackDecorator<Attribute, Void> callback)
    {
    }

    @Override
    protected AsyncCallback<MetaClass> getCallback(Presenter p, Attribute value, OnStartCallback<Void> incomeCallback)
    {
        return new BasicCallback<>();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.HINT;
    }

    @Override
    protected CallbackPresenter<Attribute, MetaClass> getPresenter(Attribute attribute)
    {
        return presenterFactory.create(context, attribute);
    }
}