package ru.naumen.core.client.content.sccase.agrserv.property;

import java.util.function.Predicate;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.FormContextHolder;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.tree.view.TreeFactoryContext;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.WidgetStyleUpdater;
import ru.naumen.core.client.widgets.WidgetStyleUpdater.WidgetTypeCode;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.client.widgets.clselect.dto.SelectDtOGinModule.AgreementServiceTreeListDataProviderFactory;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.sccase.agrserv.item.AgreementServiceItem;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

/**
 * Построитель свойства Соглашение/Услуга в виде иерархического списка
 * Первым уровнем идут соглашения, в них вложены услуги
 * Если в настройках соглашения выбирать нельзя, то они будут недоступны для выбора,
 * если услуги нельзя выбирать, то они не попадут в список
 *
 * <AUTHOR>
 * @since 04.02.2013
 */
public class AgreementServicePropertyTreeListProvider implements AgreementServicePropertyProvider<SelectItem>
{
    @Inject
    private AgreementServiceTreeListDataProviderFactory dataProviderFactory;
    @Inject
    private WidgetResources resources;
    @Inject
    private Provider<SimpleSelectCellListBuilder<IAgreementService>> selectCellListBuilderProvider;
    @Inject
    private WidgetStyleUpdater styleUpdater;
    @Inject
    private FormContextHolder formContextHolder;

    @Override
    public void createProperty(TreeFactoryContext context, AsyncCallback<Property<SelectItem>> callback)
    {
        SimpleSelectCellListBuilder<IAgreementService> listBuilder = selectCellListBuilderProvider
                .get();
        listBuilder.setMultiSelect(false);
        listBuilder.setDataProvider(dataProviderFactory.create(context));
        listBuilder.withEmptyOption();
        listBuilder.setHasSearch(true);
        listBuilder.setSelectSingleValue(formContextHolder.needSelectSingleValueForAttribute(true));
        SingleSelectCellList<IAgreementService> w = listBuilder.build();

        Predicate<SelectItem> selectableFilter = value -> Constants.EMPTY.equals(value.getCode())
                                                          || SelectItemValueExtractor.<IAgreementService> extract(value)
                                                                  .isSelectable();

        listBuilder.getCell().setSelectableFilter(selectableFilter);

        w.setSelectableFilter(selectableFilter);

        w.setStyleName(resources.form().formSelectSelected());
        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, w);
        styleUpdater.setValidationHandler(WidgetTypeCode.SIMPLE, w);

        callback.onSuccess(new PropertyBase<SelectItem, SingleSelectCellList<IAgreementService>>(w));
    }

    @Override
    public IAgreementService getValue(DtObject agreement, DtObject service)
    {
        return agreement == null ? null : new AgreementServiceItem(agreement, service);
    }
}
