package ru.naumen.core.client.content.sccase.agrserv.property;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.tree.view.TreeFactoryContext;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

/**
 * Интерфейс построителя свойства Соглашение/Услуга 
 *
 * <AUTHOR>
 * @since 04.02.2013
 */
public interface AgreementServicePropertyProvider<T>
{
    void createProperty(TreeFactoryContext context, AsyncCallback<Property<T>> callback);

    /**
     * @return элемент свойства с указанными соглашением и услугой
     */
    IAgreementService getValue(DtObject agreement, DtObject service);
}
