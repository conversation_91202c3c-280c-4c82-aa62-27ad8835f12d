package ru.naumen.core.client.content.sccase.agrserv.property;

import java.util.function.Predicate;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.content.sccase.agrserv.context.AgreementServiceContext;
import ru.naumen.core.client.widgets.WidgetStyleUpdater.WidgetTypeCode;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;

/**
 * <AUTHOR>
 * @since 13.08.2025
 */
public class AgreementServiceHierarchicalTreeProvider implements AgreementServicePropertyProvider
{
    @Override
    public void createProperty(AgreementServiceContext context, AsyncCallback callback)
    {


//        SimpleSelectCellListBuilder<IAgreementService> listBuilder = selectCellListBuilderProvider
//                .get();
//        listBuilder.setMultiSelect(false);
//        listBuilder.setDataProvider(dataProviderFactory.create(context));
//        listBuilder.withEmptyOption();
//        listBuilder.setHasSearch(true);
//        listBuilder.setSelectSingleValue(formContextHolder.needSelectSingleValueForAttribute(true));
//        SingleSelectCellList<IAgreementService> w = listBuilder.build();
//
//        Predicate<SelectItem> selectableFilter = value -> Constants.EMPTY.equals(value.getCode())
//                || SelectItemValueExtractor.<IAgreementService> extract(value)
//                .isSelectable();
//
//        listBuilder.getCell().setSelectableFilter(selectableFilter);
//
//        w.setSelectableFilter(selectableFilter);
//
//        w.setStyleName(resources.form().formSelectSelected());
//        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, w);
//        styleUpdater.setValidationHandler(WidgetTypeCode.SIMPLE, w);
//
//        callback.onSuccess(new PropertyBase<SelectItem, SingleSelectCellList<IAgreementService>>(w));
    }

    @Override
    public IAgreementService getValue(DtObject agreement, DtObject service)
    {
        return null;
    }
}