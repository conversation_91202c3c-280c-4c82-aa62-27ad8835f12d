package ru.naumen.core.shared.settings;

import static com.google.common.collect.Lists.newArrayList;
import static ru.naumen.core.shared.settings.AgreementServiceEditPrs.*;

import java.io.Serializable;
import java.util.ArrayList;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.HasTitleCode;

/**
 * Перечисление определяющее значение настройки ограничения привязки запросов
 *
 * <AUTHOR>
 * @since 06.02.2013
 */
public enum AgreementServiceSetting implements HasCode, Serializable, IsSerializable, HasTitleCode
{
    Agreement(newArrayList(List, FoldersTree), "SCParametersMessages.agreement"), // только соглашения
    Both(newArrayList(TreeList, List, HierarchicalTreeList), "SCParametersMessages.agreementOrService"), // Соглашения и/или Услуги
    Service(newArrayList(TreeList, List, FoldersTree, ServiceTreeList, HierarchicalServiceTreeList),
            "SCParametersMessages.service"); // только Услуги

    private ArrayList<AgreementServiceEditPrs> editPresentations;
    private String titleCode;

    private AgreementServiceSetting(ArrayList<AgreementServiceEditPrs> editPresentations, String titleCode)
    {
        this.editPresentations = editPresentations;
        this.titleCode = titleCode;
    }

    @Override
    public String getCode()
    {
        return this.name();
    }

    public AgreementServiceEditPrs getDefaultEditPresentation()
    {
        return editPresentations.get(0);
    }

    public ArrayList<AgreementServiceEditPrs> getEditPresentations()
    {
        return editPresentations;
    }

    @Override
    public String getTitleCode()
    {
        return titleCode;
    }

    public boolean isWithAgreements()
    {
        return this == Agreement || this == Both;
    }

    public boolean isWithServices()
    {
        return this == Service || this == Both;
    }
}
